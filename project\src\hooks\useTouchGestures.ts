import { useEffect, useRef, useState, useCallback } from 'react';
import { useDeviceInfo } from '../utils/deviceCompatibility';

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

interface SwipeGesture {
  direction: 'left' | 'right' | 'up' | 'down';
  distance: number;
  velocity: number;
  duration: number;
}

interface PinchGesture {
  scale: number;
  center: { x: number; y: number };
}

interface TapGesture {
  x: number;
  y: number;
  timestamp: number;
}

interface UseTouchGesturesOptions {
  onSwipe?: (gesture: SwipeGesture) => void;
  onPinch?: (gesture: PinchGesture) => void;
  onTap?: (gesture: TapGesture) => void;
  onDoubleTap?: (gesture: TapGesture) => void;
  onLongPress?: (gesture: TapGesture) => void;
  swipeThreshold?: number;
  pinchThreshold?: number;
  longPressDelay?: number;
  doubleTapDelay?: number;
  preventDefault?: boolean;
}

const useTouchGestures = (options: UseTouchGesturesOptions = {}) => {
  const {
    onSwipe,
    onPinch,
    onTap,
    onDoubleTap,
    onLongPress,
    swipeThreshold = 50,
    pinchThreshold = 0.1,
    longPressDelay = 500,
    doubleTapDelay = 300,
    preventDefault = true
  } = options;

  const { isTouch } = useDeviceInfo();
  const elementRef = useRef<HTMLElement>(null);
  const touchStartRef = useRef<TouchPoint | null>(null);
  const touchesRef = useRef<Touch[]>([]);
  const lastTapRef = useRef<TapGesture | null>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [isLongPressing, setIsLongPressing] = useState(false);

  // Calculate distance between two points
  const getDistance = useCallback((point1: TouchPoint, point2: TouchPoint): number => {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }, []);

  // Calculate angle between two points
  const getAngle = useCallback((point1: TouchPoint, point2: TouchPoint): number => {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    return Math.atan2(dy, dx) * 180 / Math.PI;
  }, []);

  // Get swipe direction from angle
  const getSwipeDirection = useCallback((angle: number): 'left' | 'right' | 'up' | 'down' => {
    const absAngle = Math.abs(angle);
    
    if (absAngle <= 45) return 'right';
    if (absAngle >= 135) return 'left';
    if (angle > 0) return 'down';
    return 'up';
  }, []);

  // Handle touch start
  const handleTouchStart = useCallback((event: TouchEvent) => {
    if (preventDefault) {
      event.preventDefault();
    }

    const touch = event.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    };

    touchesRef.current = Array.from(event.touches);
    setIsLongPressing(false);

    // Start long press timer for single touch
    if (event.touches.length === 1 && onLongPress) {
      longPressTimerRef.current = setTimeout(() => {
        setIsLongPressing(true);
        if (touchStartRef.current) {
          onLongPress({
            x: touchStartRef.current.x,
            y: touchStartRef.current.y,
            timestamp: touchStartRef.current.timestamp
          });
        }
      }, longPressDelay);
    }
  }, [preventDefault, onLongPress, longPressDelay]);

  // Handle touch move
  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (preventDefault) {
      event.preventDefault();
    }

    // Clear long press timer on move
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    // Handle pinch gesture
    if (event.touches.length === 2 && onPinch && touchesRef.current.length === 2) {
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      const prevTouch1 = touchesRef.current[0];
      const prevTouch2 = touchesRef.current[1];

      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );

      const prevDistance = Math.sqrt(
        Math.pow(prevTouch2.clientX - prevTouch1.clientX, 2) +
        Math.pow(prevTouch2.clientY - prevTouch1.clientY, 2)
      );

      const scale = currentDistance / prevDistance;
      
      if (Math.abs(scale - 1) > pinchThreshold) {
        const centerX = (touch1.clientX + touch2.clientX) / 2;
        const centerY = (touch1.clientY + touch2.clientY) / 2;

        onPinch({
          scale,
          center: { x: centerX, y: centerY }
        });
      }
    }

    touchesRef.current = Array.from(event.touches);
  }, [preventDefault, onPinch, pinchThreshold]);

  // Handle touch end
  const handleTouchEnd = useCallback((event: TouchEvent) => {
    if (preventDefault) {
      event.preventDefault();
    }

    // Clear long press timer
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    // Don't process if long press was triggered
    if (isLongPressing) {
      setIsLongPressing(false);
      return;
    }

    const touchEnd = {
      x: event.changedTouches[0].clientX,
      y: event.changedTouches[0].clientY,
      timestamp: Date.now()
    };

    if (!touchStartRef.current) return;

    const distance = getDistance(touchStartRef.current, touchEnd);
    const duration = touchEnd.timestamp - touchStartRef.current.timestamp;
    const velocity = distance / duration;

    // Handle swipe gesture
    if (distance >= swipeThreshold && onSwipe) {
      const angle = getAngle(touchStartRef.current, touchEnd);
      const direction = getSwipeDirection(angle);

      onSwipe({
        direction,
        distance,
        velocity,
        duration
      });
    }
    // Handle tap gestures
    else if (distance < swipeThreshold) {
      const tapGesture: TapGesture = {
        x: touchEnd.x,
        y: touchEnd.y,
        timestamp: touchEnd.timestamp
      };

      // Check for double tap
      if (lastTapRef.current && onDoubleTap) {
        const timeDiff = touchEnd.timestamp - lastTapRef.current.timestamp;
        const tapDistance = getDistance(
          { x: lastTapRef.current.x, y: lastTapRef.current.y, timestamp: 0 },
          { x: touchEnd.x, y: touchEnd.y, timestamp: 0 }
        );

        if (timeDiff <= doubleTapDelay && tapDistance < 50) {
          onDoubleTap(tapGesture);
          lastTapRef.current = null;
          return;
        }
      }

      // Single tap
      if (onTap) {
        onTap(tapGesture);
      }

      lastTapRef.current = tapGesture;

      // Clear last tap after double tap delay
      setTimeout(() => {
        if (lastTapRef.current === tapGesture) {
          lastTapRef.current = null;
        }
      }, doubleTapDelay);
    }

    touchStartRef.current = null;
    touchesRef.current = [];
  }, [preventDefault, isLongPressing, getDistance, getAngle, getSwipeDirection, swipeThreshold, onSwipe, onTap, onDoubleTap, doubleTapDelay]);

  // Attach event listeners
  useEffect(() => {
    const element = elementRef.current;
    if (!element || !isTouch) return;

    const options = { passive: !preventDefault };

    element.addEventListener('touchstart', handleTouchStart, options);
    element.addEventListener('touchmove', handleTouchMove, options);
    element.addEventListener('touchend', handleTouchEnd, options);

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
      
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
      }
    };
  }, [isTouch, preventDefault, handleTouchStart, handleTouchMove, handleTouchEnd]);

  return {
    ref: elementRef,
    isLongPressing
  };
};

export default useTouchGestures;
export type { SwipeGesture, PinchGesture, TapGesture, UseTouchGesturesOptions };