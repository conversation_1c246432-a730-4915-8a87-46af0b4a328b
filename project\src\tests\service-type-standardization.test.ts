/**
 * Service Type Standardization Tests
 * 
 * Comprehensive tests to ensure service type standardization works correctly
 * across all forms, API calls, and database operations.
 * 
 * CRITICAL: These tests verify the fixes for service type inconsistencies
 * that were causing booking failures.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  ServiceTypeStandardizer,
  standardizeFormServiceType,
  prepareServiceTypeForDb,
  prepareServiceTypeForDisplay,
  standardizeBookingData,
  validateAndFixServiceType,
  batchMigrateServiceTypes
} from '../lib/services/serviceTypeStandardizer';

import {
  normalizeServiceType,
  getServiceDisplayName,
  getServiceDbValue,
  isValidServiceType,
  migrateLegacyServiceType,
  getAllValidDbValues
} from '../lib/services/serviceTypeRegistry';

describe('Service Type Standardization', () => {
  
  describe('Service Type Registry', () => {
    it('should normalize common service type variations', () => {
      // Test residential service types
      expect(normalizeServiceType('Regular House Cleaning')).toBe('residential_regular');
      expect(normalizeServiceType('regular')).toBe('residential_regular');
      expect(normalizeServiceType('house')).toBe('residential_regular');
      
      expect(normalizeServiceType('Deep House Cleaning')).toBe('residential_deep');
      expect(normalizeServiceType('deep')).toBe('residential_deep');
      
      expect(normalizeServiceType('Move-in/Move-out Cleaning')).toBe('residential_move');
      expect(normalizeServiceType('move')).toBe('residential_move');
      expect(normalizeServiceType('move-out')).toBe('residential_move');
      
      // Test commercial service types
      expect(normalizeServiceType('office')).toBe('office');
      expect(normalizeServiceType('commercial')).toBe('office');
      expect(normalizeServiceType('carpet')).toBe('carpet');
      expect(normalizeServiceType('window')).toBe('window');
    });

    it('should return valid database values', () => {
      expect(getServiceDbValue('residential_regular')).toBe('residential_regular');
      expect(getServiceDbValue('office')).toBe('office');
      expect(getServiceDbValue('carpet')).toBe('carpet');
    });

    it('should validate service types correctly', () => {
      expect(isValidServiceType('residential_regular')).toBe(true);
      expect(isValidServiceType('office')).toBe(true);
      expect(isValidServiceType('invalid_type')).toBe(false);
      expect(isValidServiceType('')).toBe(false);
    });

    it('should get correct display names', () => {
      expect(getServiceDisplayName('residential_regular')).toBe('Regular House Cleaning');
      expect(getServiceDisplayName('office')).toBe('Office Cleaning');
      expect(getServiceDisplayName('invalid_type')).toBe('Cleaning Service');
    });

    it('should migrate legacy service types', () => {
      expect(migrateLegacyServiceType('Regular House Cleaning')).toBe('residential_regular');
      expect(migrateLegacyServiceType('Deep House Cleaning')).toBe('residential_deep');
      expect(migrateLegacyServiceType('Commercial Sanitization Service')).toBe('sanitization');
    });

    it('should return all valid database values', () => {
      const dbValues = getAllValidDbValues();
      expect(dbValues).toContain('residential_regular');
      expect(dbValues).toContain('residential_deep');
      expect(dbValues).toContain('office');
      expect(dbValues).toContain('carpet');
      expect(dbValues.length).toBeGreaterThan(10);
    });
  });

  describe('Form Data Standardization', () => {
    it('should standardize form service types', () => {
      const formData = {
        serviceType: 'Regular House Cleaning',
        firstName: 'John',
        lastName: 'Doe'
      };

      const standardized = standardizeFormServiceType(formData);
      
      expect(standardized.serviceType).toBe('residential_regular');
      expect(standardized.metadata.originalServiceType).toBe('Regular House Cleaning');
      expect(standardized.metadata.standardizedServiceType).toBe('residential_regular');
      expect(standardized.firstName).toBe('John'); // Other fields preserved
    });

    it('should handle different service type field names', () => {
      const formData1 = { serviceType: 'deep' };
      const formData2 = { service_type: 'deep' };
      const formData3 = { type: 'deep' };

      expect(standardizeFormServiceType(formData1).serviceType).toBe('residential_deep');
      expect(standardizeFormServiceType(formData2).service_type).toBe('residential_deep');
      expect(standardizeFormServiceType(formData3).type).toBe('residential_deep');
    });

    it('should prepare service types for database', () => {
      expect(prepareServiceTypeForDb('Regular House Cleaning')).toBe('residential_regular');
      expect(prepareServiceTypeForDb('deep')).toBe('residential_deep');
      expect(prepareServiceTypeForDb('invalid_type')).toBe('residential'); // fallback
    });

    it('should prepare service types for display', () => {
      expect(prepareServiceTypeForDisplay('residential_regular')).toBe('Regular House Cleaning');
      expect(prepareServiceTypeForDisplay('office')).toBe('Office Cleaning');
    });
  });

  describe('Booking Data Standardization', () => {
    it('should standardize booking data for database insertion', () => {
      const bookingData = {
        user_id: 'user123',
        service_type: 'Regular House Cleaning',
        contact: { firstName: 'John', lastName: 'Doe' },
        property_details: { type: 'house' },
        service_details: { frequency: 'weekly' }
      };

      const standardized = standardizeBookingData(bookingData);
      
      expect(standardized.service_type).toBe('residential_regular');
      expect(standardized.metadata.originalServiceType).toBe('Regular House Cleaning');
      expect(standardized.service_details.standardizedServiceType).toBe('residential_regular');
      expect(standardized.service_details.displayName).toBe('Regular House Cleaning');
    });

    it('should handle missing service_type gracefully', () => {
      const bookingData = {
        user_id: 'user123',
        contact: { firstName: 'John' }
      };

      const standardized = standardizeBookingData(bookingData);
      expect(standardized).toEqual(bookingData); // No changes if no service_type
    });
  });

  describe('Validation and Migration', () => {
    it('should validate and fix service type inconsistencies', () => {
      const testCases = [
        {
          data: { service_type: 'Regular House Cleaning' },
          expected: { isValid: true, fixed: 'residential_regular' }
        },
        {
          data: { serviceType: 'deep' },
          expected: { isValid: true, fixed: 'residential_deep' }
        },
        {
          data: { service_type: 'invalid_type' },
          expected: { isValid: true, fixed: 'residential' } // fallback
        },
        {
          data: { other_field: 'value' },
          expected: { isValid: false, fixed: 'residential' } // no service type
        }
      ];

      testCases.forEach(({ data, expected }) => {
        const result = validateAndFixServiceType(data);
        expect(result.isValid).toBe(expected.isValid);
        expect(result.fixed).toBe(expected.fixed);
      });
    });

    it('should batch migrate service types', () => {
      const dataArray = [
        { id: 1, service_type: 'Regular House Cleaning', name: 'Booking 1' },
        { id: 2, service_type: 'deep', name: 'Booking 2' },
        { id: 3, service_type: 'invalid_type', name: 'Booking 3' },
        { id: 4, serviceType: 'office', name: 'Booking 4' }
      ];

      const result = batchMigrateServiceTypes(dataArray);
      
      expect(result.summary.total).toBe(4);
      expect(result.summary.successful).toBe(4);
      expect(result.summary.failed).toBe(0);
      
      expect(result.migrated[0].service_type).toBe('residential_regular');
      expect(result.migrated[1].service_type).toBe('residential_deep');
      expect(result.migrated[2].service_type).toBe('residential'); // fallback
      expect(result.migrated[3].serviceType).toBe('office');
    });
  });

  describe('ServiceTypeStandardizer Integration', () => {
    it('should provide all standardization functions', () => {
      expect(typeof ServiceTypeStandardizer.standardizeFormServiceType).toBe('function');
      expect(typeof ServiceTypeStandardizer.prepareServiceTypeForDb).toBe('function');
      expect(typeof ServiceTypeStandardizer.standardizeBookingData).toBe('function');
      expect(typeof ServiceTypeStandardizer.normalize).toBe('function');
      expect(typeof ServiceTypeStandardizer.getDisplayName).toBe('function');
      expect(typeof ServiceTypeStandardizer.isValid).toBe('function');
    });

    it('should handle real-world form data scenarios', () => {
      // Scenario 1: Regular cleaning form
      const regularFormData = {
        propertyType: 'house',
        cleaningType: 'regular',
        serviceType: 'Regular House Cleaning',
        firstName: 'John',
        email: '<EMAIL>'
      };

      const standardizedRegular = ServiceTypeStandardizer.standardizeFormServiceType(regularFormData);
      expect(standardizedRegular.serviceType).toBe('residential_regular');

      // Scenario 2: Deep cleaning form
      const deepFormData = {
        propertyType: 'apartment',
        cleaningType: 'deep',
        serviceType: 'Deep House Cleaning'
      };

      const standardizedDeep = ServiceTypeStandardizer.standardizeFormServiceType(deepFormData);
      expect(standardizedDeep.serviceType).toBe('residential_deep');

      // Scenario 3: Commercial form
      const commercialFormData = {
        propertyType: 'office',
        serviceType: 'commercial'
      };

      const standardizedCommercial = ServiceTypeStandardizer.standardizeFormServiceType(commercialFormData);
      expect(standardizedCommercial.serviceType).toBe('office');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null and undefined inputs', () => {
      expect(normalizeServiceType('')).toBe('residential');
      expect(normalizeServiceType(null as any)).toBe('residential');
      expect(normalizeServiceType(undefined as any)).toBe('residential');
    });

    it('should handle malformed data gracefully', () => {
      const malformedData = {
        service_type: null,
        other_field: 'value'
      };

      const result = validateAndFixServiceType(malformedData);
      expect(result.fixed).toBe('residential');
      expect(result.isValid).toBe(false);
    });

    it('should preserve other form data during standardization', () => {
      const formData = {
        serviceType: 'regular',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        nested: {
          property: 'value'
        }
      };

      const standardized = standardizeFormServiceType(formData);
      
      expect(standardized.firstName).toBe('John');
      expect(standardized.lastName).toBe('Doe');
      expect(standardized.email).toBe('<EMAIL>');
      expect(standardized.nested.property).toBe('value');
      expect(standardized.serviceType).toBe('residential_regular');
    });
  });
});
