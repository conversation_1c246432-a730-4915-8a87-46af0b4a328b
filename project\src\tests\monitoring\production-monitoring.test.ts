/**
 * Production Monitoring System Tests
 * 
 * Comprehensive tests for the production monitoring infrastructure
 * including metrics collection, logging, alerting, and dashboard functionality.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ProductionMonitor } from '../../lib/monitoring/productionMonitor';
import { BookingMonitoringIntegration } from '../../lib/monitoring/bookingMonitoringIntegration';
import { AlertingSystem } from '../../lib/monitoring/alertingSystem';

// Mock external dependencies
vi.mock('../../lib/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        limit: vi.fn(() => Promise.resolve({ error: null })),
        eq: vi.fn(() => ({
          head: vi.fn(() => Promise.resolve({ count: 5, error: null }))
        }))
      }))
    }))
  }
}));

describe('Production Monitoring System', () => {
  let monitor: ProductionMonitor;
  let alertingSystem: AlertingSystem;

  beforeEach(() => {
    vi.clearAllMocks();
    monitor = ProductionMonitor.getInstance();
    alertingSystem = AlertingSystem.getInstance();
  });

  describe('ProductionMonitor', () => {
    it('should initialize successfully', async () => {
      await expect(monitor.initialize()).resolves.not.toThrow();
    });

    it('should record metrics correctly', () => {
      monitor.recordMetric('test.metric', 100, 'count', { source: 'test' });
      
      const metrics = monitor.getMetrics('test.metric');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].name).toBe('test.metric');
      expect(metrics[0].value).toBe(100);
      expect(metrics[0].unit).toBe('count');
      expect(metrics[0].tags.source).toBe('test');
    });

    it('should log events with proper categorization', () => {
      monitor.log('info', 'business', 'Test business event', { orderId: '123' });
      
      const logs = monitor.getLogs('info', 'business');
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe('info');
      expect(logs[0].category).toBe('business');
      expect(logs[0].message).toBe('Test business event');
      expect(logs[0].context.orderId).toBe('123');
    });

    it('should track system health metrics', () => {
      const health = monitor.getSystemHealth();
      
      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('uptime');
      expect(health).toHaveProperty('responseTime');
      expect(health).toHaveProperty('errorRate');
      expect(health).toHaveProperty('activeUsers');
      expect(health).toHaveProperty('pendingBookings');
      expect(health).toHaveProperty('paymentSuccessRate');
      expect(health).toHaveProperty('lastUpdated');
    });

    it('should generate dashboard data', () => {
      // Add some test data
      monitor.recordMetric('booking.started', 5, 'count');
      monitor.recordMetric('payment.completed', 3, 'count');
      monitor.log('info', 'business', 'Test event');

      const dashboard = monitor.getDashboardData();
      
      expect(dashboard).toHaveProperty('health');
      expect(dashboard).toHaveProperty('recentMetrics');
      expect(dashboard).toHaveProperty('recentLogs');
      expect(dashboard).toHaveProperty('alerts');
      
      expect(Object.keys(dashboard.recentMetrics)).toContain('booking.started');
      expect(Object.keys(dashboard.recentMetrics)).toContain('payment.completed');
    });

    it('should filter metrics by time range', () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000).toISOString();
      const futureTime = new Date(now.getTime() + 60 * 1000).toISOString(); // 1 minute in future

      monitor.recordMetric('test.recent', 1, 'count');

      const recentMetrics = monitor.getMetrics('test.recent', oneHourAgo);
      expect(recentMetrics).toHaveLength(1);

      const oldMetrics = monitor.getMetrics('test.recent', futureTime);
      expect(oldMetrics).toHaveLength(0);
    });

    it('should maintain buffer limits', () => {
      // Test metrics buffer limit
      for (let i = 0; i < 1200; i++) {
        monitor.recordMetric('test.buffer', i, 'count');
      }
      
      const metrics = monitor.getMetrics('test.buffer');
      expect(metrics.length).toBeLessThanOrEqual(1000);
      
      // Test logs buffer limit
      for (let i = 0; i < 600; i++) {
        monitor.log('info', 'system', `Test log ${i}`);
      }
      
      const logs = monitor.getLogs();
      expect(logs.length).toBeLessThanOrEqual(500);
    });
  });

  describe('BookingMonitoringIntegration', () => {
    it('should track booking journey stages', () => {
      const bookingData = {
        userId: 'user-123',
        sessionId: 'session-456',
        bookingId: 'booking-789',
        serviceType: 'residential_regular',
        amount: 150,
        duration: 1500,
        success: true
      };

      BookingMonitoringIntegration.trackBookingJourney('form_start', bookingData);
      BookingMonitoringIntegration.trackBookingJourney('payment_initiation', bookingData);
      BookingMonitoringIntegration.trackBookingJourney('booking_confirmation', bookingData);

      const metrics = monitor.getMetrics();
      const bookingMetrics = metrics.filter(m => m.name.startsWith('booking.'));
      
      expect(bookingMetrics.length).toBeGreaterThan(0);
      expect(bookingMetrics.some(m => m.name === 'booking.started')).toBe(true);
      expect(bookingMetrics.some(m => m.name === 'booking.payment_initiated')).toBe(true);
      expect(bookingMetrics.some(m => m.name === 'booking.booking_confirmed')).toBe(true);
    });

    it('should track form interactions', () => {
      BookingMonitoringIntegration.trackFormInteraction('field_focus', 'email', {
        userId: 'user-123',
        serviceType: 'residential_regular'
      });

      BookingMonitoringIntegration.trackFormInteraction('validation_error', 'email', {
        validationErrors: ['Invalid email format'],
        serviceType: 'residential_regular'
      });

      const logs = monitor.getLogs('info', 'user');
      expect(logs.some(log => log.message.includes('form_field_focus'))).toBe(true);

      const metrics = monitor.getMetrics();
      expect(metrics.some(m => m.name === 'form.validation_error')).toBe(true);
    });

    it('should track payment processing events', () => {
      const paymentData = {
        paymentId: 'payment-123',
        bookingId: 'booking-456',
        amount: 150,
        currency: 'USD',
        status: 'completed',
        duration: 2000
      };

      BookingMonitoringIntegration.trackPaymentProcessing('link_creation', paymentData);
      BookingMonitoringIntegration.trackPaymentProcessing('completion', paymentData);

      const metrics = monitor.getMetrics();
      const paymentMetrics = metrics.filter(m => m.name.startsWith('payment.'));
      
      expect(paymentMetrics.length).toBeGreaterThan(0);
      expect(paymentMetrics.some(m => m.name === 'payment.initiated')).toBe(true);
      expect(paymentMetrics.some(m => m.name === 'payment.completed')).toBe(true);
    });

    it('should track user behavior', () => {
      BookingMonitoringIntegration.trackUserBehavior('page_view', {
        userId: 'user-123',
        page: '/booking/residential',
        timeOnPage: 30000
      });

      BookingMonitoringIntegration.trackUserBehavior('button_click', {
        buttonId: 'submit-booking',
        userId: 'user-123'
      });

      const logs = monitor.getLogs('info', 'user');
      expect(logs.some(log => log.message.includes('page_view'))).toBe(true);
      expect(logs.some(log => log.message.includes('button_click'))).toBe(true);
    });

    it('should track business metrics', () => {
      BookingMonitoringIntegration.trackBusinessMetrics('booking_conversion', 85.5, {
        period: 'daily',
        serviceType: 'residential_regular'
      });

      BookingMonitoringIntegration.trackBusinessMetrics('average_booking_value', 175.50, {
        period: 'weekly'
      });

      const metrics = monitor.getMetrics();
      expect(metrics.some(m => m.name === 'business.booking_conversion')).toBe(true);
      expect(metrics.some(m => m.name === 'business.average_booking_value')).toBe(true);
    });

    it('should track security events', () => {
      BookingMonitoringIntegration.trackSecurityEvent('suspicious_activity', {
        userId: 'user-123',
        ipAddress: '***********',
        details: { reason: 'Multiple failed attempts' }
      });

      const logs = monitor.getLogs('warn', 'security');
      expect(logs.some(log => log.message.includes('suspicious_activity'))).toBe(true);

      const metrics = monitor.getMetrics();
      expect(metrics.some(m => m.name === 'security.suspicious_activity')).toBe(true);
    });

    it('should track errors with context', () => {
      const error = new Error('Database connection failed');
      
      BookingMonitoringIntegration.trackError(error, {
        operation: 'booking_creation',
        userId: 'user-123',
        bookingId: 'booking-456',
        severity: 'high'
      });

      const logs = monitor.getLogs('error', 'system');
      expect(logs.some(log => log.message.includes('Database connection failed'))).toBe(true);

      const metrics = monitor.getMetrics();
      expect(metrics.some(m => m.name === 'system.error')).toBe(true);
    });
  });

  describe('AlertingSystem', () => {
    it('should initialize with default rules', async () => {
      await alertingSystem.initialize();
      
      // The system should have default alert rules
      expect(alertingSystem).toBeDefined();
    });

    it('should add custom alert rules', () => {
      const ruleId = alertingSystem.addAlertRule({
        name: 'Test Alert Rule',
        description: 'Test rule for unit testing',
        condition: {
          type: 'metric_threshold',
          metricName: 'test.metric',
          operator: '>',
          threshold: 100,
          timeWindow: 5
        },
        severity: 'medium',
        enabled: true,
        cooldownMinutes: 10,
        notificationChannels: ['test-channel']
      });

      expect(ruleId).toBeDefined();
      expect(typeof ruleId).toBe('string');
    });

    it('should add notification channels', () => {
      const channelId = alertingSystem.addNotificationChannel({
        type: 'webhook',
        name: 'Test Webhook',
        config: { url: 'https://example.com/webhook' },
        enabled: true,
        severityFilter: ['high', 'critical']
      });

      expect(channelId).toBeDefined();
      expect(typeof channelId).toBe('string');
    });

    it('should trigger and resolve alerts', async () => {
      const alertId = await alertingSystem.triggerAlert(
        'Test Alert',
        'medium',
        'This is a test alert',
        { testData: 'value' }
      );

      expect(alertId).toBeDefined();

      const activeAlerts = alertingSystem.getActiveAlerts();
      expect(activeAlerts.some(alert => alert.id === alertId)).toBe(true);

      const resolved = alertingSystem.resolveAlert(alertId, 'test-user');
      expect(resolved).toBe(true);

      const activeAlertsAfterResolve = alertingSystem.getActiveAlerts();
      expect(activeAlertsAfterResolve.some(alert => alert.id === alertId)).toBe(false);
    });

    it('should filter alerts by severity', async () => {
      await alertingSystem.triggerAlert('Low Alert', 'low', 'Low severity alert');
      await alertingSystem.triggerAlert('High Alert', 'high', 'High severity alert');
      await alertingSystem.triggerAlert('Critical Alert', 'critical', 'Critical severity alert');

      const highAlerts = alertingSystem.getActiveAlerts('high');
      const criticalAlerts = alertingSystem.getActiveAlerts('critical');

      expect(highAlerts.every(alert => alert.severity === 'high')).toBe(true);
      expect(criticalAlerts.every(alert => alert.severity === 'critical')).toBe(true);
    });

    it('should provide alert statistics', async () => {
      await alertingSystem.triggerAlert('Test Alert 1', 'low', 'Test message');
      await alertingSystem.triggerAlert('Test Alert 2', 'high', 'Test message');
      
      const stats = alertingSystem.getAlertStatistics();
      
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('active');
      expect(stats).toHaveProperty('resolved');
      expect(stats).toHaveProperty('bySeverity');
      expect(stats).toHaveProperty('byRule');
      
      expect(stats.total).toBeGreaterThan(0);
      expect(stats.bySeverity.low).toBeGreaterThan(0);
      expect(stats.bySeverity.high).toBeGreaterThan(0);
    });

    it('should get alert history', async () => {
      await alertingSystem.triggerAlert('Historical Alert', 'medium', 'Test message');
      
      const history = alertingSystem.getAlertHistory(10);
      
      expect(Array.isArray(history)).toBe(true);
      expect(history.length).toBeGreaterThan(0);
      expect(history[0]).toHaveProperty('id');
      expect(history[0]).toHaveProperty('timestamp');
      expect(history[0]).toHaveProperty('severity');
    });
  });

  describe('Integration Tests', () => {
    it('should integrate monitoring with booking operations', async () => {
      // Simulate a complete booking flow with monitoring
      const startTime = Date.now();
      
      // Track booking start
      BookingMonitoringIntegration.trackBookingJourney('form_start', {
        userId: 'integration-user',
        sessionId: 'integration-session',
        serviceType: 'residential_regular'
      });

      // Track form completion
      BookingMonitoringIntegration.trackBookingJourney('form_validation', {
        userId: 'integration-user',
        bookingId: 'integration-booking',
        duration: 5000,
        success: true
      });

      // Track payment
      BookingMonitoringIntegration.trackPaymentProcessing('link_creation', {
        paymentId: 'integration-payment',
        bookingId: 'integration-booking',
        amount: 150,
        duration: 1000
      });

      // Track completion
      BookingMonitoringIntegration.trackBookingJourney('booking_confirmation', {
        userId: 'integration-user',
        bookingId: 'integration-booking',
        success: true,
        duration: Date.now() - startTime
      });

      // Verify metrics were recorded
      const metrics = monitor.getMetrics();
      const bookingMetrics = metrics.filter(m => m.name.startsWith('booking.'));
      const paymentMetrics = metrics.filter(m => m.name.startsWith('payment.'));
      
      expect(bookingMetrics.length).toBeGreaterThan(0);
      expect(paymentMetrics.length).toBeGreaterThan(0);

      // Verify logs were created
      const logs = monitor.getLogs();
      const businessLogs = logs.filter(l => l.category === 'business');
      
      expect(businessLogs.length).toBeGreaterThan(0);
    });

    it('should handle error scenarios with proper monitoring', () => {
      const error = new Error('Payment processing failed');
      
      // Track the error
      BookingMonitoringIntegration.trackError(error, {
        operation: 'payment_processing',
        userId: 'error-user',
        paymentId: 'failed-payment',
        severity: 'high'
      });

      // Track failed payment
      BookingMonitoringIntegration.trackPaymentProcessing('completion', {
        paymentId: 'failed-payment',
        status: 'failed',
        errorMessage: error.message
      });

      // Verify error tracking
      const errorLogs = monitor.getLogs('error', 'system');
      expect(errorLogs.some(log => log.message.includes('Payment processing failed'))).toBe(true);

      const errorMetrics = monitor.getMetrics('system.error');
      expect(errorMetrics.length).toBeGreaterThan(0);
    });
  });
});
