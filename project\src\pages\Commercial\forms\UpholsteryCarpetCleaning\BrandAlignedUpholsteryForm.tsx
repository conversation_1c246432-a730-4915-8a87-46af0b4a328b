import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Sofa, Layers, Sparkles, Wind, Droplets, Building, CheckCircle, AlertCircle
} from 'lucide-react';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { supabase } from '../../../../lib/supabase/client';
import { submitBookingForm } from '../../../../lib/api/bookingForms';

// Data Interfaces
interface UpholsteryFormData {
  servicePackage?: string;
  propertyType?: string;
  itemTypes?: string[];
  squareFootage?: number;
  itemCount?: number;
  fabricTypes?: string[];
  propertyAddress?: string;
  accessHours?: string;
  securityRequirements?: string;
  parkingAvailable?: boolean;
  serviceFrequency?: string;
  preferredTime?: string;
  priorityAreas?: string[];
  additionalServices?: string[];
  specialInstructions?: string;
  startDate?: string;
  wantsRecurringContract?: boolean;
  contractLength?: string;
  budgetRange?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  jobTitle?: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  propertyAddress?: string;
  propertyType?: string;
  itemTypes?: string;
  squareFootage?: string;
  serviceFrequency?: string;
  preferredTime?: string;
}

// Validation Utilities
const validateEmail = (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
const validatePhoneNumber = (phone: string) => /^\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/.test(phone);
const formatPhoneNumber = (value: string) => {
  if (!value) return value;
  const phoneNumber = value.replace(/[^\d]/g, '');
  const phoneNumberLength = phoneNumber.length;
  if (phoneNumberLength < 4) return phoneNumber;
  if (phoneNumberLength < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  }
  return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
};

// Form Constants
const steps = [
  { id: 1, name: 'Service' },
  { id: 2, name: 'Property' },
  { id: 3, name: 'Scope' },
  { id: 4, name: 'Add-ons' },
  { id: 5, name: 'Schedule' },
  { id: 6, name: 'Contact' },
];

const upholsteryServices = [
  { id: 'office-furniture', name: 'Office Furniture Cleaning', description: 'Chairs, sofas, and fabric panels.', icon: <Sofa /> },
  { id: 'upholstery-deep', name: 'Deep Upholstery Cleaning', description: 'Steam cleaning for heavy soiling.', icon: <Layers /> },
  { id: 'fabric-restoration', name: 'Fabric Restoration', description: 'Color restoration & fabric protection.', icon: <Sparkles /> },
  { id: 'stain-treatment', name: 'Stain Treatment', description: 'Specialized stain removal service.', icon: <Droplets /> },
  { id: 'maintenance-program', name: 'Maintenance Program', description: 'Regular upholstery care schedule.', icon: <Wind /> },
];

const propertyTypes = [
  { id: 'office-building', name: 'Office Building' },
  { id: 'retail-store', name: 'Retail Store' },
  { id: 'restaurant', name: 'Restaurant/Cafe' },
  { id: 'hotel', name: 'Hotel/Hospitality' },
  { id: 'medical-facility', name: 'Medical Facility' },
  { id: 'school-university', name: 'School/University' },
  { id: 'showroom', name: 'Vehicle Showroom' },
  { id: 'warehouse', name: 'Warehouse/Industrial' },
  { id: 'other', name: 'Other' },
];

const itemTypes = [
  { id: 'office-chairs', name: 'Office Chairs' },
  { id: 'sofas-couches', name: 'Sofas & Couches' },
  { id: 'carpet-areas', name: 'Carpet Areas' },
  { id: 'cubicle-panels', name: 'Cubicle Panels' },
  { id: 'conference-chairs', name: 'Conference Chairs' },
  { id: 'lobby-furniture', name: 'Lobby Furniture' },
  { id: 'area-rugs', name: 'Area Rugs' },
  { id: 'drapes-curtains', name: 'Drapes & Curtains' },
];

const fabricTypes = [
  { id: 'synthetic', name: 'Synthetic Fabric' },
  { id: 'wool', name: 'Wool' },
  { id: 'cotton', name: 'Cotton' },
  { id: 'leather', name: 'Leather' },
  { id: 'microfiber', name: 'Microfiber' },
  { id: 'mixed', name: 'Mixed Materials' },
];

const serviceFrequencies = [
  { id: 'one-time', name: 'One-Time Service' },
  { id: 'quarterly', name: 'Quarterly' },
  { id: 'bi-annual', name: 'Bi-Annual' },
  { id: 'annual', name: 'Annual' },
  { id: 'custom', name: 'Custom Schedule' },
];

const preferredTimes = [
  { id: 'after-hours', name: 'After Business Hours (Recommended)' },
  { id: 'morning', name: 'Morning (8AM - 12PM)' },
  { id: 'afternoon', name: 'Afternoon (12PM - 5PM)' },
  { id: 'weekend', name: 'Weekends' },
];

const priorityAreas = [
  { id: 'reception', name: 'Reception/Lobby' },
  { id: 'conference-rooms', name: 'Conference Rooms' },
  { id: 'executive-offices', name: 'Executive Offices' },
  { id: 'break-rooms', name: 'Break Rooms' },
  { id: 'customer-areas', name: 'Customer Areas' },
  { id: 'high-traffic', name: 'High-Traffic Zones' },
];

const additionalServices = [
  { id: 'odor-treatment', name: 'Odor Treatment & Deodorizing' },
  { id: 'fabric-protection', name: 'Fabric Protection Application' },
  { id: 'spot-cleaning', name: 'Spot Cleaning Service' },
  { id: 'pet-stain-removal', name: 'Pet Stain & Odor Removal' },
  { id: 'allergen-treatment', name: 'Allergen Reduction Treatment' },
  { id: 'emergency-service', name: 'Emergency Spill Response' },
];

const BrandAlignedUpholsteryForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<UpholsteryFormData>({});
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState<'success' | 'error' | null>(null);

  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        firstName: prev.firstName || user.user_metadata?.first_name,
        lastName: prev.lastName || user.user_metadata?.last_name,
        email: prev.email || user.email,
        phone: prev.phone || user.phone,
        companyName: prev.companyName || user.user_metadata?.company_name,
      }));
    }
  }, [user]);

  const validateField = (fieldName: keyof FormErrors, value: string | number | string[] | undefined): string | undefined => {
    switch (fieldName) {
      case 'email':
        if (!value || typeof value !== 'string') return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value || typeof value !== 'string') return 'Phone number is required';
        if (!validatePhoneNumber(value)) return 'Please enter a valid phone number';
        break;
      case 'firstName':
      case 'lastName':
      case 'companyName':
      case 'propertyAddress':
        if (!value || typeof value !== 'string' || value.trim().length < 2) return `${fieldName.replace(/([A-Z])/g, ' $1')} must be at least 2 characters`;
        break;
      case 'propertyType':
        if (!value || typeof value !== 'string') return 'Please select a property type';
        break;
      case 'itemTypes':
        if (!value || !Array.isArray(value) || value.length === 0) return 'Please select at least one item type';
        break;
      case 'squareFootage':
        if (!value || typeof value !== 'number' || value < 100) return 'Square footage must be at least 100 sq ft';
        break;
      case 'serviceFrequency':
        if (!value || typeof value !== 'string') return 'Please select a service frequency';
        break;
      case 'preferredTime':
        if (!value || typeof value !== 'string') return 'Please select a preferred time';
        break;
    }
    return undefined;
  };

  const handleInputChange = (field: keyof UpholsteryFormData, value: string | number | boolean | string[] | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (Object.keys(formErrors).includes(field) && field in ({} as FormErrors)) {
      // Only validate non-boolean fields
      if (typeof value !== 'boolean') {
        const error = validateField(field as keyof FormErrors, value as string | number | string[] | undefined);
        setFormErrors(prev => ({ ...prev, [field]: error }));
      }
    }
  };
  
  const handleNextStep = () => {
    let isValid = true;
    const newErrors: FormErrors = {};

    // Validate fields for the current step
    if (currentStep === 1) {
      if (!formData.servicePackage) {
        // This is a soft validation, just for UI state, not blocking
      }
    } else if (currentStep === 2) {
        ['propertyType', 'itemTypes', 'squareFootage', 'propertyAddress'].forEach(field => {
            const value = formData[field as keyof UpholsteryFormData];
            if (typeof value !== 'boolean') {
                const error = validateField(field as keyof FormErrors, value);
                if (error) {
                    newErrors[field as keyof FormErrors] = error;
                    isValid = false;
                }
            }
        });
    } else if (currentStep === 3) {
        ['serviceFrequency', 'preferredTime'].forEach(field => {
            const value = formData[field as keyof UpholsteryFormData];
            if (typeof value !== 'boolean') {
                const error = validateField(field as keyof FormErrors, value);
                if (error) {
                    newErrors[field as keyof FormErrors] = error;
                    isValid = false;
                }
            }
        });
    } else if (currentStep === 6) {
        ['firstName', 'lastName', 'email', 'phone', 'companyName'].forEach(field => {
            const value = formData[field as keyof UpholsteryFormData];
            if (typeof value !== 'boolean') {
                const error = validateField(field as keyof FormErrors, value);
                if (error) {
                    newErrors[field as keyof FormErrors] = error;
                    isValid = false;
                }
            }
        });
    }

    setFormErrors(newErrors);

    if (isValid) {
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      navigate('/commercial');
    }
  };
  
  const handleSubmit = async () => {
    setIsSubmitting(true);
    // Final validation check
     const newErrors: FormErrors = {};
    ['firstName', 'lastName', 'email', 'phone', 'companyName', 'propertyAddress', 'propertyType', 'itemTypes', 'squareFootage', 'serviceFrequency', 'preferredTime'].forEach(field => {
        const value = formData[field as keyof UpholsteryFormData];
        if (typeof value !== 'boolean') {
            const error = validateField(field as keyof FormErrors, value);
            if (error) {
                newErrors[field as keyof FormErrors] = error;
            }
        }
    });
     if (Object.keys(newErrors).length > 0) {
        setFormErrors(newErrors);
        setIsSubmitting(false);
        // Maybe jump to the first step with an error
        if (newErrors.propertyType || newErrors.itemTypes || newErrors.squareFootage || newErrors.propertyAddress) setCurrentStep(2);
        else if (newErrors.serviceFrequency || newErrors.preferredTime) setCurrentStep(3);
        else if (newErrors.firstName || newErrors.lastName || newErrors.email || newErrors.phone || newErrors.companyName) setCurrentStep(6);
        return;
    }

    try {
      // Prepare the submission data for the centralized API
      const submissionData = {
        serviceType: 'upholstery & carpet',
        requestType: 'booking',
        submittedAt: new Date().toISOString(),
        contactInfo: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          companyName: formData.companyName,
          jobTitle: formData.jobTitle,
        },
        propertyDetails: {
          propertyType: formData.propertyType,
          propertyAddress: formData.propertyAddress,
          squareFootage: formData.squareFootage,
          itemTypes: formData.itemTypes,
          itemCount: formData.itemCount,
          fabricTypes: formData.fabricTypes,
          accessHours: formData.accessHours,
          securityRequirements: formData.securityRequirements,
          parkingAvailable: formData.parkingAvailable,
        },
        serviceDetails: {
          servicePackage: formData.servicePackage,
          serviceFrequency: formData.serviceFrequency,
          preferredTime: formData.preferredTime,
          priorityAreas: formData.priorityAreas,
          additionalServices: formData.additionalServices,
          specialInstructions: formData.specialInstructions,
          budgetRange: formData.budgetRange,
          startDate: formData.startDate,
          wantsRecurringContract: formData.wantsRecurringContract,
          contractLength: formData.contractLength,
        },
      };

      console.log('Submitting upholstery & carpet request:', submissionData);

      // Use the centralized booking API
      const result = await submitBookingForm(submissionData);

      if (result.success) {
        console.log('Successfully submitted:', result.data);
        setSubmissionStatus('success');
        
        // Clear form data from localStorage after successful submission
        localStorage.removeItem('pending_upholstery_request');
        
        // Navigate to appropriate page based on authentication status
        setTimeout(() => {
          if (user) {
            navigate('/accountdashboard');
          } else {
            // Store the intent to redirect to dashboard after login
            localStorage.setItem('redirectAfterLogin', '/accountdashboard');
            navigate('/auth/login');
          }
        }, 2000);
      } else {
        console.error('Booking submission failed:', result.error);
        throw new Error(result.error || 'Submission failed');
      }
    } catch (error) {
      console.error('Submission Error:', error);
      
      // Fallback: Save to localStorage for later retry
      try {
        const fallbackData = {
          timestamp: new Date().toISOString(),
          formData: formData,
          type: 'upholstery_request'
        };
        localStorage.setItem('pending_upholstery_request', JSON.stringify(fallbackData));
        console.log('Saved request to localStorage for later retry');
      } catch (storageError) {
        console.error('Failed to save to localStorage:', storageError);
      }
      
      setSubmissionStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Continue with renderStepContent and rest of component...
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div key="step1" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Select Your Service Package</h2>
            <p className="text-gray-300 mb-6">Choose the cleaning package that best fits your needs.</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {upholsteryServices.map((pkg) => (
                <motion.div
                  key={pkg.id}
                  whileHover={{ scale: 1.05, boxShadow: '0px 10px 30px rgba(0, 255, 135, 0.15)' }}
                  className={`p-6 rounded-2xl border transition-all duration-300 cursor-pointer
                    ${formData.servicePackage === pkg.id
                      ? 'bg-green-500/10 border-green-400'
                      : 'bg-white/5 border-white/20 hover:border-green-400/50'
                    }`}
                  onClick={() => handleInputChange('servicePackage', pkg.id)}
                >
                  <div className="flex items-center gap-4 mb-3">
                    <div className="text-green-400">{pkg.icon}</div>
                    <h3 className="font-semibold text-white text-lg">{pkg.name}</h3>
                  </div>
                  <p className="text-gray-300 text-sm">{pkg.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div key="step2" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Property & Item Details</h2>
            <p className="text-gray-300 mb-6">Tell us about the space and items we'll be cleaning.</p>
            
            <div className="space-y-8">
              {/* Property Information Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Property Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Property Type *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {propertyTypes.slice(0, 6).map((type) => (
                        <motion.div
                          key={type.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200 text-center
                            ${formData.propertyType === type.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('propertyType', type.id)}
                        >
                          <Building className="w-4 h-4 mx-auto mb-1 text-green-400" />
                          <span className="text-xs font-medium text-white">{type.name}</span>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.propertyType && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.propertyType}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Property Address *</label>
                      <input
                        type="text"
                        className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                          ${formErrors.propertyAddress ? 'border-red-400' : 'border-white/20'}
                          focus:border-green-400 focus:outline-none transition-colors`}
                        placeholder="Enter property address"
                        value={formData.propertyAddress || ''}
                        onChange={(e) => handleInputChange('propertyAddress', e.target.value)}
                      />
                      {formErrors.propertyAddress && (
                        <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                          <AlertCircle className="w-3 h-3" />
                          {formErrors.propertyAddress}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Total Square Footage *</label>
                      <input
                        type="number"
                        className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                          ${formErrors.squareFootage ? 'border-red-400' : 'border-white/20'}
                          focus:border-green-400 focus:outline-none transition-colors`}
                        placeholder="e.g., 5000"
                        min="100"
                        value={formData.squareFootage || ''}
                        onChange={(e) => handleInputChange('squareFootage', +e.target.value)}
                      />
                      {formErrors.squareFootage && (
                        <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                          <AlertCircle className="w-3 h-3" />
                          {formErrors.squareFootage}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Item Types Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Items to Clean</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Item Types (Select all that apply) *</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {itemTypes.map((item) => (
                        <motion.div
                          key={item.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                            ${formData.itemTypes?.includes(item.id)
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => {
                            const currentItems = formData.itemTypes || [];
                            const newItems = currentItems.includes(item.id)
                              ? currentItems.filter(i => i !== item.id)
                              : [...currentItems, item.id];
                            handleInputChange('itemTypes', newItems);
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <div className={`w-4 h-4 rounded border-2 flex items-center justify-center
                              ${formData.itemTypes?.includes(item.id)
                                ? 'bg-green-400 border-green-400'
                                : 'border-white/30'
                              }`}>
                              {formData.itemTypes?.includes(item.id) && (
                                <CheckCircle className="w-2 h-2 text-white" />
                              )}
                            </div>
                            <span className="text-sm font-medium text-white">{item.name}</span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.itemTypes && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.itemTypes}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Approximate Item Count</label>
                      <input
                        type="number"
                        className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                          focus:border-green-400 focus:outline-none transition-colors"
                        placeholder="e.g., 25 chairs"
                        min="1"
                        value={formData.itemCount || ''}
                        onChange={(e) => handleInputChange('itemCount', +e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Primary Fabric Types</label>
                      <select
                        className="w-full p-3 rounded-xl bg-slate-900 border border-white/20 text-white
                          focus:border-green-400 focus:outline-none transition-colors
                          hover:bg-slate-800 hover:border-slate-600"
                        value={formData.fabricTypes?.[0] || ''}
                        onChange={(e) => handleInputChange('fabricTypes', [e.target.value])}
                        style={{
                          backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                          backgroundPosition: 'right 0.5rem center',
                          backgroundRepeat: 'no-repeat',
                          backgroundSize: '1.5em 1.5em',
                          paddingRight: '2.5rem'
                        }}
                      >
                        <option value="" style={{ backgroundColor: '#0f172a', color: '#9ca3af' }}>
                          Select fabric type
                        </option>
                        {fabricTypes.map((fabric) => (
                          <option key={fabric.id} value={fabric.id} style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                            {fabric.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div key="step3" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Service Requirements</h2>
            <p className="text-gray-300 mb-6">Let us know your scheduling and access requirements.</p>
            
            <div className="space-y-8">
              {/* Service Schedule Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Service Schedule</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Service Frequency *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {serviceFrequencies.map((freq) => (
                        <motion.div
                          key={freq.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200 text-center
                            ${formData.serviceFrequency === freq.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('serviceFrequency', freq.id)}
                        >
                          <div className="text-sm font-medium text-white">{freq.name}</div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.serviceFrequency && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.serviceFrequency}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Preferred Service Time *</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {preferredTimes.map((time) => (
                        <motion.div
                          key={time.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                            ${formData.preferredTime === time.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('preferredTime', time.id)}
                        >
                          <div className="text-sm font-medium text-white">{time.name}</div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.preferredTime && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.preferredTime}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Priority Areas Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Priority Areas</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">Focus Areas (Optional)</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {priorityAreas.map((area) => (
                      <motion.div
                        key={area.id}
                        whileHover={{ scale: 1.02 }}
                        className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                          ${formData.priorityAreas?.includes(area.id)
                            ? 'bg-green-500/10 border-green-400'
                            : 'bg-white/5 border-white/20 hover:border-green-400/50'
                          }`}
                        onClick={() => {
                          const currentAreas = formData.priorityAreas || [];
                          const newAreas = currentAreas.includes(area.id)
                            ? currentAreas.filter(a => a !== area.id)
                            : [...currentAreas, area.id];
                          handleInputChange('priorityAreas', newAreas);
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-4 h-4 rounded border-2 flex items-center justify-center
                            ${formData.priorityAreas?.includes(area.id)
                              ? 'bg-green-400 border-green-400'
                              : 'border-white/30'
                            }`}>
                            {formData.priorityAreas?.includes(area.id) && (
                              <CheckCircle className="w-2 h-2 text-white" />
                            )}
                          </div>
                          <span className="text-sm font-medium text-white">{area.name}</span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Access & Logistics Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Access & Logistics</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Access Hours</label>
                      <input
                        type="text"
                        className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                          focus:border-green-400 focus:outline-none transition-colors"
                        placeholder="e.g., 6 PM - 6 AM weekdays"
                        value={formData.accessHours || ''}
                        onChange={(e) => handleInputChange('accessHours', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Security Requirements</label>
                      <input
                        type="text"
                        className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                          focus:border-green-400 focus:outline-none transition-colors"
                        placeholder="e.g., Badge access required"
                        value={formData.securityRequirements || ''}
                        onChange={(e) => handleInputChange('securityRequirements', e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Parking Available for Service Vehicles?</label>
                    <div className="flex gap-4">
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className={`flex-1 p-3 rounded-xl border cursor-pointer transition-all duration-200
                          ${formData.parkingAvailable === true
                            ? 'bg-green-500/10 border-green-400'
                            : 'bg-white/5 border-white/20 hover:border-green-400/50'
                          }`}
                        onClick={() => handleInputChange('parkingAvailable', true)}
                      >
                        <div className="text-center text-sm font-medium text-white">Yes</div>
                      </motion.div>
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className={`flex-1 p-3 rounded-xl border cursor-pointer transition-all duration-200
                          ${formData.parkingAvailable === false
                            ? 'bg-green-500/10 border-green-400'
                            : 'bg-white/5 border-white/20 hover:border-green-400/50'
                          }`}
                        onClick={() => handleInputChange('parkingAvailable', false)}
                      >
                        <div className="text-center text-sm font-medium text-white">No</div>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div key="step4" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Additional Services</h2>
            <p className="text-gray-300 mb-6">Select any additional services you'd like to include.</p>
            
            <div className="space-y-6">
              {/* Additional Services */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Add-On Services (Optional)</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {additionalServices.map((service) => (
                    <motion.div
                      key={service.id}
                      whileHover={{ scale: 1.02 }}
                      className={`p-4 rounded-xl border cursor-pointer transition-all duration-200
                        ${formData.additionalServices?.includes(service.id)
                          ? 'bg-green-500/10 border-green-400'
                          : 'bg-white/5 border-white/20 hover:border-green-400/50'
                        }`}
                      onClick={() => {
                        const currentServices = formData.additionalServices || [];
                        const newServices = currentServices.includes(service.id)
                          ? currentServices.filter(s => s !== service.id)
                          : [...currentServices, service.id];
                        handleInputChange('additionalServices', newServices);
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center
                          ${formData.additionalServices?.includes(service.id)
                            ? 'bg-green-400 border-green-400'
                            : 'border-white/30'
                          }`}>
                          {formData.additionalServices?.includes(service.id) && (
                            <CheckCircle className="w-3 h-3 text-white" />
                          )}
                        </div>
                        <span className="text-sm font-medium text-white">{service.name}</span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Special Instructions */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Special Instructions or Requirements</label>
                <textarea
                  className="w-full p-4 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                    focus:border-green-400 focus:outline-none transition-colors h-24 resize-none"
                  placeholder="Any specific requirements, stain concerns, or instructions for our team..."
                  value={formData.specialInstructions || ''}
                  onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                />
              </div>

              {/* Budget Range */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Budget Range (Optional)</label>
                <select
                  className="w-full p-4 rounded-xl bg-slate-900 border border-white/20 text-white
                    focus:border-green-400 focus:outline-none transition-colors
                    hover:bg-slate-800 hover:border-slate-600"
                  value={formData.budgetRange || ''}
                  onChange={(e) => handleInputChange('budgetRange', e.target.value)}
                  style={{
                    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                    backgroundPosition: 'right 0.5rem center',
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: '1.5em 1.5em',
                    paddingRight: '2.5rem'
                  }}
                >
                  <option value="" style={{ backgroundColor: '#0f172a', color: '#9ca3af' }}>
                    Select budget range
                  </option>
                  <option value="under-500" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    Under $500
                  </option>
                  <option value="500-1000" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    $500 - $1,000
                  </option>
                  <option value="1000-2500" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    $1,000 - $2,500
                  </option>
                  <option value="2500-5000" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    $2,500 - $5,000
                  </option>
                  <option value="over-5000" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    Over $5,000
                  </option>
                </select>
              </div>
            </div>
          </motion.div>
        );

      case 5:
        return (
          <motion.div key="step5" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Schedule & Contract</h2>
            <p className="text-gray-300 mb-6">When would you like to start and what are your contract preferences?</p>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Preferred Start Date</label>
                  <input
                    type="date"
                    className="w-full p-4 rounded-xl bg-white/5 border border-white/20 text-white
                      focus:border-green-400 focus:outline-none transition-colors"
                    value={formData.startDate || ''}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Contract Preference</label>
                  <div className="space-y-3">
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                        ${formData.wantsRecurringContract === false
                          ? 'bg-green-500/10 border-green-400'
                          : 'bg-white/5 border-white/20 hover:border-green-400/50'
                        }`}
                      onClick={() => handleInputChange('wantsRecurringContract', false)}
                    >
                      <div className="text-sm font-medium text-white">One-Time Service</div>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                        ${formData.wantsRecurringContract === true
                          ? 'bg-green-500/10 border-green-400'
                          : 'bg-white/5 border-white/20 hover:border-green-400/50'
                        }`}
                      onClick={() => handleInputChange('wantsRecurringContract', true)}
                    >
                      <div className="text-sm font-medium text-white">Recurring Contract</div>
                    </motion.div>
                  </div>
                </div>
              </div>

              {formData.wantsRecurringContract && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  <label className="block text-sm font-medium text-gray-300 mb-2">Preferred Contract Length</label>
                  <select
                    className="w-full p-4 rounded-xl bg-slate-900 border border-white/20 text-white
                      focus:border-green-400 focus:outline-none transition-colors
                      hover:bg-slate-800 hover:border-slate-600"
                    value={formData.contractLength || ''}
                    onChange={(e) => handleInputChange('contractLength', e.target.value)}
                    style={{
                      backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                      backgroundPosition: 'right 0.5rem center',
                      backgroundRepeat: 'no-repeat',
                      backgroundSize: '1.5em 1.5em',
                      paddingRight: '2.5rem'
                    }}
                  >
                    <option value="" style={{ backgroundColor: '#0f172a', color: '#9ca3af' }}>
                      Select contract length
                    </option>
                    <option value="6-months" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                      6 Months
                    </option>
                    <option value="1-year" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                      1 Year
                    </option>
                    <option value="2-years" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                      2 Years
                    </option>
                    <option value="3-years" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                      3 Years
                    </option>
                  </select>
                </motion.div>
              )}
            </div>
          </motion.div>
        );

      case 6:
        return (
          <motion.div key="step6" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
            <p className="text-gray-300 mb-6">Please provide your contact details to complete your request.</p>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">First Name *</label>
                  <input
                    type="text"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.firstName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your first name"
                    value={formData.firstName || ''}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                  />
                  {formErrors.firstName && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.firstName}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Last Name *</label>
                  <input
                    type="text"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.lastName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your last name"
                    value={formData.lastName || ''}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                  />
                  {formErrors.lastName && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.lastName}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                  <input
                    type="email"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.email ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your email"
                    value={formData.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                  />
                  {formErrors.email && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.email}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number *</label>
                  <input
                    type="tel"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.phone ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="(*************"
                    value={formData.phone || ''}
                    onChange={(e) => handleInputChange('phone', formatPhoneNumber(e.target.value))}
                  />
                  {formErrors.phone && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.phone}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Company Name *</label>
                  <input
                    type="text"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.companyName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your company name"
                    value={formData.companyName || ''}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                  />
                  {formErrors.companyName && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.companyName}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Job Title</label>
                  <input
                    type="text"
                    className="w-full p-4 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                      focus:border-green-400 focus:outline-none transition-colors"
                    placeholder="Enter your job title"
                    value={formData.jobTitle || ''}
                    onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div key="step2" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Property & Item Details</h2>
            <p className="text-gray-300 mb-6">Tell us about the space and items we'll be cleaning.</p>
            
            <div className="space-y-8">
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Property Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Property Type *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {propertyTypes.slice(0, 6).map((type) => (
                        <motion.div
                          key={type.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200 text-center
                            ${formData.propertyType === type.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('propertyType', type.id)}
                        >
                          <Building className="w-4 h-4 mx-auto mb-1 text-green-400" />
                          <span className="text-xs font-medium text-white">{type.name}</span>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.propertyType && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.propertyType}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Property Address *</label>
                      <input
                        type="text"
                        className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                          ${formErrors.propertyAddress ? 'border-red-400' : 'border-white/20'}
                          focus:border-green-400 focus:outline-none transition-colors`}
                        placeholder="Enter property address"
                        value={formData.propertyAddress || ''}
                        onChange={(e) => handleInputChange('propertyAddress', e.target.value)}
                      />
                      {formErrors.propertyAddress && (
                        <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                          <AlertCircle className="w-3 h-3" />
                          {formErrors.propertyAddress}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Total Square Footage *</label>
                      <input
                        type="number"
                        className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                          ${formErrors.squareFootage ? 'border-red-400' : 'border-white/20'}
                          focus:border-green-400 focus:outline-none transition-colors`}
                        placeholder="e.g., 5000"
                        min="100"
                        value={formData.squareFootage || ''}
                        onChange={(e) => handleInputChange('squareFootage', +e.target.value)}
                      />
                      {formErrors.squareFootage && (
                        <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                          <AlertCircle className="w-3 h-3" />
                          {formErrors.squareFootage}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Items to Clean</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Item Types (Select all that apply) *</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {itemTypes.map((item) => (
                        <motion.div
                          key={item.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                            ${formData.itemTypes?.includes(item.id)
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => {
                            const currentItems = formData.itemTypes || [];
                            const newItems = currentItems.includes(item.id)
                              ? currentItems.filter(i => i !== item.id)
                              : [...currentItems, item.id];
                            handleInputChange('itemTypes', newItems);
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <div className={`w-4 h-4 rounded border-2 flex items-center justify-center
                              ${formData.itemTypes?.includes(item.id)
                                ? 'bg-green-400 border-green-400'
                                : 'border-white/30'
                              }`}>
                              {formData.itemTypes?.includes(item.id) && (
                                <CheckCircle className="w-2 h-2 text-white" />
                              )}
                            </div>
                            <span className="text-sm font-medium text-white">{item.name}</span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.itemTypes && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.itemTypes}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div key="step3" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Service Requirements</h2>
            <p className="text-gray-300 mb-6">Let us know your scheduling and access requirements.</p>
            
            <div className="space-y-8">
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Service Schedule</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Service Frequency *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {serviceFrequencies.map((freq) => (
                        <motion.div
                          key={freq.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200 text-center
                            ${formData.serviceFrequency === freq.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('serviceFrequency', freq.id)}
                        >
                          <div className="text-sm font-medium text-white">{freq.name}</div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.serviceFrequency && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.serviceFrequency}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Preferred Service Time *</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {preferredTimes.map((time) => (
                        <motion.div
                          key={time.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                            ${formData.preferredTime === time.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('preferredTime', time.id)}
                        >
                          <div className="text-sm font-medium text-white">{time.name}</div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.preferredTime && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.preferredTime}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div key="step4" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Additional Services</h2>
            <p className="text-gray-300 mb-6">Select any additional services you'd like to include.</p>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Add-On Services (Optional)</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {additionalServices.map((service) => (
                    <motion.div
                      key={service.id}
                      whileHover={{ scale: 1.02 }}
                      className={`p-4 rounded-xl border cursor-pointer transition-all duration-200
                        ${formData.additionalServices?.includes(service.id)
                          ? 'bg-green-500/10 border-green-400'
                          : 'bg-white/5 border-white/20 hover:border-green-400/50'
                        }`}
                      onClick={() => {
                        const currentServices = formData.additionalServices || [];
                        const newServices = currentServices.includes(service.id)
                          ? currentServices.filter(s => s !== service.id)
                          : [...currentServices, service.id];
                        handleInputChange('additionalServices', newServices);
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center
                          ${formData.additionalServices?.includes(service.id)
                            ? 'bg-green-400 border-green-400'
                            : 'border-white/30'
                          }`}>
                          {formData.additionalServices?.includes(service.id) && (
                            <CheckCircle className="w-3 h-3 text-white" />
                          )}
                        </div>
                        <span className="text-sm font-medium text-white">{service.name}</span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Special Instructions</label>
                <textarea
                  className="w-full p-4 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                    focus:border-green-400 focus:outline-none transition-colors h-24 resize-none"
                  placeholder="Any specific requirements, stain concerns, or instructions..."
                  value={formData.specialInstructions || ''}
                  onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                />
              </div>
            </div>
          </motion.div>
        );

      case 5:
        return (
          <motion.div key="step5" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Schedule & Contract</h2>
            <p className="text-gray-300 mb-6">When would you like to start?</p>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Preferred Start Date</label>
                <input
                  type="date"
                  className="w-full p-4 rounded-xl bg-white/5 border border-white/20 text-white
                    focus:border-green-400 focus:outline-none transition-colors"
                  value={formData.startDate || ''}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                />
              </div>
            </div>
          </motion.div>
        );

      case 6:
        return (
          <motion.div key="step6" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
            <p className="text-gray-300 mb-6">Please provide your contact details.</p>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">First Name *</label>
                  <input
                    type="text"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.firstName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your first name"
                    value={formData.firstName || ''}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                  />
                  {formErrors.firstName && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.firstName}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Last Name *</label>
                  <input
                    type="text"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.lastName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your last name"
                    value={formData.lastName || ''}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                  />
                  {formErrors.lastName && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.lastName}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                  <input
                    type="email"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.email ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your email"
                    value={formData.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                  />
                  {formErrors.email && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.email}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number *</label>
                  <input
                    type="tel"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.phone ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="(*************"
                    value={formData.phone || ''}
                    onChange={(e) => handleInputChange('phone', formatPhoneNumber(e.target.value))}
                  />
                  {formErrors.phone && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.phone}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Company Name *</label>
                <input
                  type="text"
                  className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                    ${formErrors.companyName ? 'border-red-400' : 'border-white/20'}
                    focus:border-green-400 focus:outline-none transition-colors`}
                  placeholder="Enter your company name"
                  value={formData.companyName || ''}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                />
                {formErrors.companyName && (
                  <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                    <AlertCircle className="w-3 h-3" />
                    {formErrors.companyName}
                  </p>
                )}
              </div>
            </div>
          </motion.div>
        );

      default:
        return <div>Step content coming soon...</div>;
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          {/* Modern Progress Bar */}
          <div className="mb-12">
            {/* Step Counter */}
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-2 border border-white/20">
                <span className="text-white font-medium">
                  Step {currentStep} of {steps.length}
                </span>
              </div>
            </div>
            
            {/* Progress Line */}
            <div className="relative">
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"
                  initial={{ width: '16.67%' }}
                  animate={{ width: `${(currentStep / steps.length) * 100}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                />
              </div>
              
              {/* Step Dots */}
              <div className="absolute inset-0 flex items-center justify-between px-1">
                {steps.map((step) => (
                  <motion.div
                    key={step.id}
                    className={`w-4 h-4 rounded-full border-2 ${
                      currentStep >= step.id 
                        ? 'bg-green-400 border-green-400' 
                        : 'bg-white/10 border-white/30'
                    }`}
                    animate={{
                      scale: currentStep === step.id ? 1.2 : 1,
                    }}
                    transition={{ duration: 0.3 }}
                  />
                ))}
              </div>
            </div>
            
            {/* Current Step Label */}
            <div className="text-center mt-4">
              <h1 className="text-xl font-semibold text-white">
                {steps[currentStep - 1]?.name}
              </h1>
            </div>
          </div>

          {/* Form Content */}
          <motion.div 
            className="rounded-3xl shadow-2xl p-8 md:p-12"
            style={{
              background: 'linear-gradient(135deg, rgba(15, 32, 39, 0.95) 0%, rgba(32, 58, 67, 0.95) 50%, rgba(44, 85, 48, 0.95) 100%)',
              border: '1px solid rgba(34, 197, 94, 0.3)',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
              backdropFilter: 'blur(12px)',
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <AnimatePresence mode="wait">
              {renderStepContent()}
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="flex justify-between items-center mt-12">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handlePrevStep}
                className="px-8 py-4 rounded-xl border-2 border-white/20 text-white font-semibold 
                  hover:bg-white/10 hover:border-white/30 transition-all duration-300"
              >
                {currentStep === 1 ? 'Cancel' : 'Back'}
              </motion.button>

              {currentStep < steps.length ? (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleNextStep}
                  disabled={isSubmitting}
                  className="px-8 py-4 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 
                    hover:from-green-600 hover:to-emerald-700 text-white font-semibold 
                    shadow-lg disabled:opacity-50 transition-all duration-300"
                >
                  Continue
                </motion.button>
              ) : (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="px-8 py-4 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 
                    hover:from-green-600 hover:to-emerald-700 text-white font-semibold 
                    shadow-lg disabled:opacity-50 transition-all duration-300"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Request'}
                </motion.button>
              )}
            </div>

            {/* Success/Error Status */}
            {submissionStatus === 'success' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-6 p-4 bg-green-500/20 border border-green-400 rounded-xl text-center"
              >
                <CheckCircle className="w-6 h-6 text-green-400 mx-auto mb-2" />
                <p className="text-green-300 font-medium">Request submitted successfully!</p>
                <p className="text-green-200 text-sm">Redirecting to your dashboard...</p>
              </motion.div>
            )}

            {submissionStatus === 'error' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-6 p-4 bg-red-500/20 border border-red-400 rounded-xl text-center"
              >
                <AlertCircle className="w-6 h-6 text-red-400 mx-auto mb-2" />
                <p className="text-red-300 font-medium">Submission Error</p>
                <p className="text-red-200 text-sm">We encountered an issue submitting your request. Please try again or contact us directly.</p>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setSubmissionStatus(null)}
                  className="mt-3 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                >
                  Try Again
                </motion.button>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedUpholsteryForm;