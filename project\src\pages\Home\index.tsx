import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Building2, Home as HomeIcon, ArrowRight, Shield, Star, Clock, 
  Users, Sparkles, ChevronDown, CheckCircle, X, AlertCircle
} from 'lucide-react';

import { Header } from '../../components/layout/Header';
import { Footer } from '../../components/layout/Footer';
import { AnimatedBackground } from '../../components/layout/AnimatedBackground';
import { ReviewCarousel } from '../../components/testimonials/ReviewCarousel';
import AIChatBookingWidget from '../../components/AIChatBooking';

import { navigateToServiceForm } from '../../lib/utils/navigation';

// Banner text animation options
const bannerTexts = [
  "Home cleaning, made easy.",
  "Office cleaning, made simple.",
  "Commercial spaces, made spotless.",
  "Your space, our expertise."
];

const Section: React.FC<{ children: React.ReactNode, className?: string }> = ({ children, className = '' }) => (
  <section className={`py-8 sm:py-12 ${className}`}>
    {children}
  </section>
);

export function HomePage() {
  const navigate = useNavigate();
  const location = useLocation();

  const [showFAQ, setShowFAQ] = useState<number | null>(null);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [showMessage, setShowMessage] = useState(false);
  const [message, setMessage] = useState('');

  // Check for redirect messages
  useEffect(() => {
    if (location.state?.message) {
      setMessage(location.state.message);
      setShowMessage(true);
      
      // Clear the message from location state
      navigate(location.pathname, { replace: true });
      
      // Auto-hide message after 8 seconds
      setTimeout(() => {
        setShowMessage(false);
      }, 8000);
    }
  }, [location.state, navigate, location.pathname]);

  // Banner text animation
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBannerIndex((prevIndex) => (prevIndex + 1) % bannerTexts.length);
    }, 4000); // Change every 4 seconds

    return () => clearInterval(interval);
  }, []);

  const faqs = [
    {
      question: "How often should I schedule cleaning services?",
      answer: "For residential properties, we recommend weekly or bi-weekly cleaning for high-traffic homes, and monthly for less frequently used spaces. For commercial properties, daily or weekly cleaning is typically recommended depending on your business type and foot traffic."
    },
    {
      question: "Do you bring your own cleaning supplies?",
      answer: "Yes, our professional team brings all necessary cleaning supplies and equipment. We use eco-friendly, commercial-grade products that are effective yet safe for your family, pets, and the environment."
    },
    {
      question: "Are your cleaning staff insured and background checked?",
      answer: "Absolutely. All our cleaning professionals undergo thorough background checks and are fully insured. We prioritize your security and peace of mind with every service."
    },
    {
      question: "What if I'm not satisfied with the cleaning service?",
      answer: "Your satisfaction is our priority. If you're not completely satisfied with our service, contact us within 24 hours and we'll return to re-clean the areas in question at no additional cost."
    }
  ];

  return (
    <AnimatedBackground>
      <div className="relative h-screen overflow-hidden">
        <Header />
        
        <div className="h-full overflow-y-auto" style={{ paddingTop: '80px' }}>
          <AnimatePresence>
            {showMessage && (
              <motion.div
                initial={{ opacity: 0, y: -100 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -100 }}
                className="fixed top-24 left-0 right-0 z-50 mx-4"
              >
                <div className="max-w-4xl mx-auto">
                  <div className="bg-orange-100 border border-orange-300 rounded-lg p-4 shadow-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <AlertCircle className="w-5 h-5 text-orange-600 mr-3" />
                        <p className="text-orange-800 font-medium">{message}</p>
                      </div>
                      <button
                        onClick={() => setShowMessage(false)}
                        className="text-orange-600 hover:text-orange-800 transition-colors"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          
          <main>
            {/* Hero Section */}
            <Section className="!pt-12 md:!pt-20">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center">
                  <motion.div 
                    className="mb-12"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, ease: 'easeOut' }}
                  >
                    <AnimatePresence mode="wait">
                      <motion.h1
                        key={currentBannerIndex}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -30 }}
                        transition={{ duration: 0.5, ease: 'easeInOut' }}
                        className="text-5xl md:text-6xl font-bold text-white leading-tight"
                        style={{ textShadow: '0 4px 30px rgba(0,0,0,0.4)' }}
                      >
                        {bannerTexts[currentBannerIndex]}
                      </motion.h1>
                    </AnimatePresence>
                  </motion.div>
                  
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8 }}
                  >
                    <AIChatBookingWidget />
                  </motion.div>
                </div>
              </div>
            </Section>

            {/* Popular Services Section */}
            <Section className="!pb-8">
              <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="mb-12">
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                    className="text-center mb-12"
                  >
                    <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                      Popular Cleaning Services
                    </h2>
                    <motion.div
                      initial={{ scaleX: 0 }}
                      whileInView={{ scaleX: 1 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.6, delay: 0.2 }}
                      className="w-24 h-1 bg-emerald-400 mx-auto mb-6 origin-center"
                    />
                    <p className="text-lg text-white/70 max-w-2xl mx-auto">
                      Choose from our most requested professional cleaning solutions
                    </p>
                  </motion.div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[
                      { icon: HomeIcon, title: "House Cleaning", description: "Regular home cleaning", id: "regular" },
                      { icon: Sparkles, title: "Deep Cleaning", description: "Thorough deep clean", id: "deep" },
                      { icon: Building2, title: "Office Cleaning", description: "Commercial spaces", id: "office" },
                      { icon: Shield, title: "Sanitization", description: "Disinfection services", id: "sanitization" },
                      { icon: Users, title: "Post-Construction", description: "Construction cleanup", id: "construction" },
                      { icon: Clock, title: "Carpet Cleaning", description: "Carpet & upholstery", id: "carpet" },
                      { icon: Star, title: "Window Cleaning", description: "Interior & exterior", id: "window" },
                      { icon: Building2, title: "Floor Care", description: "Floor restoration", id: "floor" }
                  ].map((service, index) => {
                    const Icon = service.icon;
                    return (
                        <motion.button
                          key={service.id}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                          transition={{ delay: index * 0.05 }}
                          onClick={() => navigateToServiceForm(navigate, service.id)}
                          className="relative group bg-white/10 backdrop-blur-xl rounded-2xl p-6 text-left 
                                     border border-white/10 hover:border-white/20 transition-all duration-300
                                     hover:bg-white/20 hover:-translate-y-1 shadow-lg hover:shadow-2xl"
                        >
                          <div className="flex flex-col h-full">
                            <div className="w-12 h-12 rounded-lg bg-emerald-400/20 flex items-center justify-center mb-5
                                          group-hover:bg-emerald-400/30 transition-colors">
                              <Icon className="w-6 h-6 text-emerald-300 group-hover:text-emerald-200 transition-colors" />
                            </div>
                            <h3 className="font-semibold text-white text-lg mb-1">
                              {service.title}
                            </h3>
                            <p className="text-sm text-white/70">
                              {service.description}
                            </p>
                        </div>
                        </motion.button>
                    );
                  })}
                  </div>
                </div>

                {/* See more services link */}
                <div className="text-center mt-8">
                  <button
                    onClick={() => navigate('/services')}
                    className="text-emerald-400 hover:text-emerald-300 font-medium inline-flex items-center gap-2 transition-colors group whitespace-nowrap"
                  >
                    See all services
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            </Section>

            {/* Explore Cleaning Projects Section */}
            <Section className="!py-0">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="text-center mb-12"
                    >
                  <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    Explore Cleaning Transformations
                  </h2>
                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="w-24 h-1 bg-emerald-400 mx-auto mb-6 origin-center"
                  />
                  <p className="text-lg text-white/70 max-w-2xl mx-auto">
                    See how our professional cleaning services can transform your spaces
                  </p>
                  </motion.div>

                {/* Project Cards Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {[
                    {
                      title: "Residential Deep Clean",
                      description: "Complete home transformation",
                      image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                      tags: ["Living Spaces", "Bedrooms", "Kitchens"]
                    },
                    {
                      title: "Office Makeover",
                      description: "Professional workspace cleaning",
                      image: "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                      tags: ["Desks", "Meeting Rooms", "Common Areas"]
                    },
                    {
                      title: "Move-In Ready",
                      description: "Fresh start for new beginnings",
                      image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                      tags: ["Empty Homes", "Deep Sanitization", "Ready to Live"]
                    }
                  ].map((project, index) => (
                  <motion.div
                      key={project.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                      transition={{ delay: index * 0.1 }}
                      className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer"
                      onClick={() => navigate('/services')}
                    >
                      {/* Image Container */}
                      <div className="relative h-80 overflow-hidden">
                          <img
                          src={project.image} 
                          alt={project.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                        {/* Gradient Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                          </div>
                          
                      {/* Content Overlay */}
                      <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <h3 className="text-2xl font-bold mb-2">{project.title}</h3>
                        <p className="text-white/90 mb-4">{project.description}</p>
                        
                        {/* Tags */}
                        <div className="flex flex-wrap gap-2">
                          {project.tags.map((tag) => (
                            <span 
                              key={tag}
                              className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-xs font-medium"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Hover Arrow */}
                      <div className="absolute top-4 right-4 w-12 h-12 bg-white/10 backdrop-blur-md rounded-full 
                                    flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <ArrowRight className="w-6 h-6 text-white" />
                      </div>
                  </motion.div>
                  ))}
                </div>

                {/* View All Link */}
                <motion.div 
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.4 }}
                  className="text-center mt-12"
                >
                  <button
                    onClick={() => navigate('/services')}
                    className="inline-flex items-center gap-2 text-emerald-400 hover:text-emerald-300 font-medium text-lg group whitespace-nowrap"
                  >
                    View all cleaning services
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </button>
                </motion.div>
              </div>
            </Section>

            {/* What Makes Us Different Section */}
            <Section>
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="text-center mb-16"
                >
                  <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    What Makes Us Different
                  </h2>
                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="w-24 h-1 bg-emerald-400 mx-auto mb-6 origin-center"
                  />
                  <p className="text-lg text-white/70 max-w-2xl mx-auto">
                    Experience the perfect blend of technology, expertise, and care that sets us apart
                  </p>
                </motion.div>

                {/* Feature Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                  {[
                    {
                      title: "Smart Scheduling",
                      description: "Book, reschedule, or cancel anytime with our intuitive online platform.",
                      icon: Clock,
                      color: "blue",
                      features: ["Real-time availability", "SMS reminders", "Flexible timing"]
                    },
                    {
                      title: "Eco-Certified Clean",
                      description: "Green Seal certified products that are safe for your family and pets.",
                      icon: Sparkles,
                      color: "green",
                      features: ["Non-toxic formulas", "Biodegradable", "Allergen-free"]
                    },
                    {
                      title: "Guaranteed Results",
                      description: "Our thorough 50-point checklist ensures nothing is missed.",
                      icon: CheckCircle,
                      color: "purple",
                      features: ["Quality inspections", "Satisfaction guarantee", "24hr re-clean policy"]
                    }
                  ].map((item, index) => (
                    <motion.div
                      key={item.title}
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ 
                        delay: index * 0.1,
                        duration: 0.5,
                      }}
                      className="relative group"
                    >
                      <motion.div 
                        whileHover={{ y: -8, transition: { duration: 0.3 } }}
                        className="relative bg-white/5 backdrop-blur-xl rounded-2xl p-8 h-full border border-white/10 transition-all duration-300 group-hover:border-white/20 group-hover:shadow-2xl group-hover:shadow-black/40"
                      >
                        <div className="relative inline-block mb-6">
                          <div className={`absolute -inset-3 rounded-full opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-300
                            ${item.color === 'blue' ? 'bg-blue-400' : 
                              item.color === 'green' ? 'bg-green-400' : 
                              'bg-purple-400'}`}></div>
                          <item.icon className={`relative w-12 h-12 
                            ${item.color === 'blue' ? 'text-blue-400' : 
                              item.color === 'green' ? 'text-green-400' : 
                              'text-purple-400'}`} />
                        </div>
                        
                        <h3 className="text-xl font-bold text-white mb-3">{item.title}</h3>
                        <p className="text-white/70 mb-6">{item.description}</p>
                        
                        <ul className="space-y-3">
                          {item.features.map((feature) => (
                            <li 
                              key={feature}
                              className="flex items-center text-sm text-white/80"
                            >
                              <CheckCircle className="w-4 h-4 mr-3 flex-shrink-0 text-emerald-400" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </motion.div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </Section>

            {/* Process Timeline */}
            <Section>
              <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                      Our Proven Process
                    </h2>
                    <p className="text-lg text-white/70 max-w-2xl mx-auto">
                      Four simple steps to a spotless space.
                    </p>
                </div>
                
                <div className="relative">
                  {/* Connector Line */}
                  <div className="absolute top-1/2 left-0 w-full h-0.5 bg-white/10 -translate-y-1/2"></div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-8 relative">
                    {[
                      { step: "01", title: "Book Online", desc: "Select your service and schedule a time in 60 seconds.", icon: "🔍" },
                      { step: "02", title: "We Clean", desc: "Our certified professionals arrive on time and get to work.", icon: "✨" },
                      { step: "03", title: "You Relax", desc: "Enjoy your sparkling clean space and newfound free time.", icon: "😊" },
                      { step: "04", title: "Rate Us", desc: "Provide feedback so we can continue to improve our service.", icon: "⭐" }
                    ].map((process, index) => (
                      <motion.div 
                        key={process.step} 
                        initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true, amount: 0.5 }}
                        transition={{ delay: index * 0.1, duration: 0.5 }}
                        className="text-center group"
                      >
                        <div className="relative inline-block">
                          <motion.div
                            whileHover={{ scale: 1.1 }}
                            className="w-24 h-24 bg-white/5 backdrop-blur-xl border border-white/10
                                          rounded-full flex flex-col items-center justify-center text-white shadow-lg
                                          group-hover:border-emerald-400/50 group-hover:bg-emerald-500/10 transition-colors duration-300"
                          >
                            <span className="text-3xl font-bold text-emerald-400">{process.step}</span>
                          </motion.div>
                        </div>
                        
                        <h4 className="font-semibold text-white mt-6 mb-2 text-lg">{process.title}</h4>
                        <p className="text-sm text-white/70">{process.desc}</p>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </Section>

            {/* Testimonials Section - Using ReviewCarousel from Residential page */}
            <Section>
              <ReviewCarousel />
            </Section>

            {/* FAQ Section */}
            <Section>
              <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="text-center mb-12"
                >
                  <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    Frequently Asked Questions
                  </h2>
                  <p className="text-lg text-white/70 max-w-2xl mx-auto">
                    Have questions? We have answers.
                  </p>
                </motion.div>
                <div className="space-y-4">
                  {faqs.map((faq, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden"
                    >
                      <button
                        onClick={() => setShowFAQ(showFAQ === index ? null : index)}
                        className="w-full flex justify-between items-center p-6 text-left"
                      >
                        <span className="text-lg font-medium text-white">{faq.question}</span>
                        <motion.div
                          animate={{ rotate: showFAQ === index ? 180 : 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <ChevronDown className="w-5 h-5 text-white/70" />
                        </motion.div>
                      </button>
                      <AnimatePresence>
                        {showFAQ === index && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: 'easeInOut' }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-6 text-white/70">
                              {faq.answer}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </div>
              </div>
            </Section>
            
            <Footer />
          </main>
        </div>
      </div>
    </AnimatedBackground>
  );
}