/**
 * Production Monitoring Configuration
 * 
 * Environment-specific configuration for monitoring, alerting,
 * and observability features.
 */

export interface MonitoringConfig {
  enabled: boolean;
  environment: 'development' | 'staging' | 'production';
  
  // Metrics configuration
  metrics: {
    enabled: boolean;
    bufferSize: number;
    flushInterval: number; // milliseconds
    retentionPeriod: number; // milliseconds
  };

  // Logging configuration
  logging: {
    enabled: boolean;
    level: 'debug' | 'info' | 'warn' | 'error' | 'critical';
    bufferSize: number;
    consoleOutput: boolean;
    remoteLogging: boolean;
  };

  // Alerting configuration
  alerting: {
    enabled: boolean;
    cooldownPeriod: number; // minutes
    maxAlertsPerHour: number;
    channels: {
      email: boolean;
      slack: boolean;
      webhook: boolean;
    };
  };

  // Performance monitoring
  performance: {
    enabled: boolean;
    longTaskThreshold: number; // milliseconds
    slowRequestThreshold: number; // milliseconds
    memoryLeakDetection: boolean;
  };

  // Business metrics
  business: {
    enabled: boolean;
    conversionTracking: boolean;
    userBehaviorTracking: boolean;
    revenueTracking: boolean;
  };

  // Security monitoring
  security: {
    enabled: boolean;
    suspiciousActivityDetection: boolean;
    rateLimitMonitoring: boolean;
    failedAuthTracking: boolean;
  };

  // External services
  external: {
    datadog: {
      enabled: boolean;
      apiKey?: string;
      site?: string;
    };
    newrelic: {
      enabled: boolean;
      licenseKey?: string;
    };
    sentry: {
      enabled: boolean;
      dsn?: string;
    };
    slack: {
      enabled: boolean;
      webhookUrl?: string;
      channel?: string;
    };
  };
}

// ============================================================================
// Environment Configurations
// ============================================================================

const developmentConfig: MonitoringConfig = {
  enabled: true,
  environment: 'development',
  
  metrics: {
    enabled: true,
    bufferSize: 500,
    flushInterval: 30000, // 30 seconds
    retentionPeriod: 3600000 // 1 hour
  },

  logging: {
    enabled: true,
    level: 'debug',
    bufferSize: 200,
    consoleOutput: true,
    remoteLogging: false
  },

  alerting: {
    enabled: false, // Disabled in development
    cooldownPeriod: 5,
    maxAlertsPerHour: 10,
    channels: {
      email: false,
      slack: false,
      webhook: false
    }
  },

  performance: {
    enabled: true,
    longTaskThreshold: 50,
    slowRequestThreshold: 1000,
    memoryLeakDetection: true
  },

  business: {
    enabled: true,
    conversionTracking: true,
    userBehaviorTracking: true,
    revenueTracking: false // No real revenue in dev
  },

  security: {
    enabled: true,
    suspiciousActivityDetection: false,
    rateLimitMonitoring: false,
    failedAuthTracking: true
  },

  external: {
    datadog: { enabled: false },
    newrelic: { enabled: false },
    sentry: { enabled: false },
    slack: { enabled: false }
  }
};

const stagingConfig: MonitoringConfig = {
  enabled: true,
  environment: 'staging',
  
  metrics: {
    enabled: true,
    bufferSize: 1000,
    flushInterval: 60000, // 1 minute
    retentionPeriod: 86400000 // 24 hours
  },

  logging: {
    enabled: true,
    level: 'info',
    bufferSize: 500,
    consoleOutput: false,
    remoteLogging: true
  },

  alerting: {
    enabled: true,
    cooldownPeriod: 10,
    maxAlertsPerHour: 20,
    channels: {
      email: true,
      slack: true,
      webhook: false
    }
  },

  performance: {
    enabled: true,
    longTaskThreshold: 50,
    slowRequestThreshold: 2000,
    memoryLeakDetection: true
  },

  business: {
    enabled: true,
    conversionTracking: true,
    userBehaviorTracking: true,
    revenueTracking: true
  },

  security: {
    enabled: true,
    suspiciousActivityDetection: true,
    rateLimitMonitoring: true,
    failedAuthTracking: true
  },

  external: {
    datadog: {
      enabled: !!process.env.DATADOG_API_KEY,
      apiKey: process.env.DATADOG_API_KEY,
      site: process.env.DATADOG_SITE || 'datadoghq.com'
    },
    newrelic: {
      enabled: !!process.env.NEW_RELIC_LICENSE_KEY,
      licenseKey: process.env.NEW_RELIC_LICENSE_KEY
    },
    sentry: {
      enabled: !!process.env.SENTRY_DSN,
      dsn: process.env.SENTRY_DSN
    },
    slack: {
      enabled: !!process.env.SLACK_WEBHOOK_URL,
      webhookUrl: process.env.SLACK_WEBHOOK_URL,
      channel: process.env.SLACK_CHANNEL || '#alerts-staging'
    }
  }
};

const productionConfig: MonitoringConfig = {
  enabled: true,
  environment: 'production',
  
  metrics: {
    enabled: true,
    bufferSize: 2000,
    flushInterval: 60000, // 1 minute
    retentionPeriod: 604800000 // 7 days
  },

  logging: {
    enabled: true,
    level: 'warn',
    bufferSize: 1000,
    consoleOutput: false,
    remoteLogging: true
  },

  alerting: {
    enabled: true,
    cooldownPeriod: 15,
    maxAlertsPerHour: 50,
    channels: {
      email: true,
      slack: true,
      webhook: true
    }
  },

  performance: {
    enabled: true,
    longTaskThreshold: 50,
    slowRequestThreshold: 3000,
    memoryLeakDetection: true
  },

  business: {
    enabled: true,
    conversionTracking: true,
    userBehaviorTracking: true,
    revenueTracking: true
  },

  security: {
    enabled: true,
    suspiciousActivityDetection: true,
    rateLimitMonitoring: true,
    failedAuthTracking: true
  },

  external: {
    datadog: {
      enabled: !!process.env.DATADOG_API_KEY,
      apiKey: process.env.DATADOG_API_KEY,
      site: process.env.DATADOG_SITE || 'datadoghq.com'
    },
    newrelic: {
      enabled: !!process.env.NEW_RELIC_LICENSE_KEY,
      licenseKey: process.env.NEW_RELIC_LICENSE_KEY
    },
    sentry: {
      enabled: !!process.env.SENTRY_DSN,
      dsn: process.env.SENTRY_DSN
    },
    slack: {
      enabled: !!process.env.SLACK_WEBHOOK_URL,
      webhookUrl: process.env.SLACK_WEBHOOK_URL,
      channel: process.env.SLACK_CHANNEL || '#alerts-production'
    }
  }
};

// ============================================================================
// Configuration Management
// ============================================================================

/**
 * Get monitoring configuration for current environment
 */
export function getMonitoringConfig(): MonitoringConfig {
  const environment = import.meta.env.MODE || 'development';
  
  switch (environment) {
    case 'production':
      return productionConfig;
    case 'staging':
      return stagingConfig;
    case 'development':
    default:
      return developmentConfig;
  }
}

/**
 * Override configuration for testing
 */
export function setTestConfig(config: Partial<MonitoringConfig>): MonitoringConfig {
  return {
    ...developmentConfig,
    ...config,
    enabled: config.enabled ?? false // Disabled by default in tests
  };
}

/**
 * Validate configuration
 */
export function validateConfig(config: MonitoringConfig): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required fields
  if (!config.environment) {
    errors.push('Environment is required');
  }

  // Validate external service configurations
  if (config.external.datadog.enabled && !config.external.datadog.apiKey) {
    errors.push('DataDog API key is required when DataDog is enabled');
  }

  if (config.external.newrelic.enabled && !config.external.newrelic.licenseKey) {
    errors.push('New Relic license key is required when New Relic is enabled');
  }

  if (config.external.sentry.enabled && !config.external.sentry.dsn) {
    errors.push('Sentry DSN is required when Sentry is enabled');
  }

  if (config.external.slack.enabled && !config.external.slack.webhookUrl) {
    errors.push('Slack webhook URL is required when Slack is enabled');
  }

  // Performance warnings
  if (config.metrics.bufferSize > 5000) {
    warnings.push('Large metrics buffer size may impact memory usage');
  }

  if (config.logging.bufferSize > 2000) {
    warnings.push('Large logging buffer size may impact memory usage');
  }

  if (config.performance.longTaskThreshold < 16) {
    warnings.push('Very low long task threshold may generate excessive alerts');
  }

  // Production-specific validations
  if (config.environment === 'production') {
    if (!config.logging.remoteLogging) {
      warnings.push('Remote logging should be enabled in production');
    }

    if (config.logging.consoleOutput) {
      warnings.push('Console output should be disabled in production');
    }

    if (!config.alerting.enabled) {
      warnings.push('Alerting should be enabled in production');
    }

    if (config.logging.level === 'debug') {
      warnings.push('Debug logging should not be used in production');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Get configuration summary for debugging
 */
export function getConfigSummary(config: MonitoringConfig): Record<string, any> {
  return {
    environment: config.environment,
    enabled: config.enabled,
    features: {
      metrics: config.metrics.enabled,
      logging: config.logging.enabled,
      alerting: config.alerting.enabled,
      performance: config.performance.enabled,
      business: config.business.enabled,
      security: config.security.enabled
    },
    external: Object.entries(config.external).reduce((acc, [service, serviceConfig]) => {
      acc[service] = serviceConfig.enabled;
      return acc;
    }, {} as Record<string, boolean>),
    bufferSizes: {
      metrics: config.metrics.bufferSize,
      logging: config.logging.bufferSize
    },
    intervals: {
      metricsFlush: config.metrics.flushInterval,
      alertCooldown: config.alerting.cooldownPeriod
    }
  };
}

// Export default configuration
export default getMonitoringConfig();
