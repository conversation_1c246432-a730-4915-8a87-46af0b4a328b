/**
 * Payment Security Module
 * 
 * Enhanced security measures specifically for payment processing,
 * including fraud detection, payment validation, and secure handling
 * of sensitive payment data.
 * 
 * CRITICAL: This module addresses payment-specific security vulnerabilities
 * that could lead to financial fraud, data breaches, or compliance violations.
 */

import { BookingMonitor } from '../monitoring/bookingMonitoringIntegration';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface PaymentSecurityContext {
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  amount: number;
  currency: string;
  paymentMethod?: string;
  timestamp: number;
}

export interface FraudDetectionResult {
  riskScore: number; // 0-100, higher = more risky
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  flags: string[];
  recommendations: string[];
  allowPayment: boolean;
}

export interface PaymentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedData: any;
}

// ============================================================================
// Payment Security Class
// ============================================================================

export class PaymentSecurity {
  private static instance: PaymentSecurity;
  private suspiciousIPs: Set<string> = new Set();
  private recentPayments: Map<string, number[]> = new Map(); // userId -> timestamps
  private blockedUsers: Set<string> = new Set();

  // Security configuration
  private readonly MAX_PAYMENT_AMOUNT = 10000; // $10,000
  private readonly MIN_PAYMENT_AMOUNT = 1; // $1
  private readonly MAX_PAYMENTS_PER_HOUR = 5;
  private readonly MAX_PAYMENTS_PER_DAY = 20;
  private readonly SUSPICIOUS_AMOUNT_THRESHOLD = 5000; // $5,000
  private readonly RAPID_PAYMENT_THRESHOLD = 300000; // 5 minutes

  private constructor() {}

  static getInstance(): PaymentSecurity {
    if (!PaymentSecurity.instance) {
      PaymentSecurity.instance = new PaymentSecurity();
    }
    return PaymentSecurity.instance;
  }

  /**
   * Comprehensive payment security validation
   */
  async validatePaymentSecurity(
    paymentData: any,
    context: PaymentSecurityContext
  ): Promise<PaymentValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let sanitizedData = { ...paymentData };

    try {
      // 1. Basic amount validation
      const amountValidation = this.validatePaymentAmount(context.amount);
      if (!amountValidation.isValid) {
        errors.push(...amountValidation.errors);
      }

      // 2. Fraud detection
      const fraudResult = await this.detectFraud(context);
      if (!fraudResult.allowPayment) {
        errors.push(`Payment blocked due to fraud detection: ${fraudResult.flags.join(', ')}`);
        
        // Log security event
        BookingMonitor.trackSecurityEvent('suspicious_activity', {
          userId: context.userId,
          ipAddress: context.ipAddress,
          details: {
            riskScore: fraudResult.riskScore,
            flags: fraudResult.flags,
            amount: context.amount
          }
        });
      }

      if (fraudResult.riskLevel === 'high' || fraudResult.riskLevel === 'critical') {
        warnings.push(`High risk payment detected: ${fraudResult.flags.join(', ')}`);
      }

      // 3. Rate limiting validation
      const rateLimitResult = this.validatePaymentRateLimit(context);
      if (!rateLimitResult.allowed) {
        errors.push(rateLimitResult.message);
      }

      // 4. Sanitize payment data
      sanitizedData = this.sanitizePaymentData(paymentData);

      // 5. Validate payment data structure
      const structureValidation = this.validatePaymentDataStructure(sanitizedData);
      if (!structureValidation.isValid) {
        errors.push(...structureValidation.errors);
      }

      // 6. Check for blocked users/IPs
      if (context.userId && this.blockedUsers.has(context.userId)) {
        errors.push('Payment blocked: User account flagged for suspicious activity');
      }

      if (context.ipAddress && this.suspiciousIPs.has(context.ipAddress)) {
        warnings.push('Payment from flagged IP address');
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        sanitizedData
      };

    } catch (error) {
      BookingMonitor.trackError(error, {
        operation: 'payment_security_validation',
        userId: context.userId,
        severity: 'high',
        metadata: { amount: context.amount, currency: context.currency }
      });

      return {
        isValid: false,
        errors: ['Payment security validation failed'],
        warnings: [],
        sanitizedData: {}
      };
    }
  }

  /**
   * Fraud detection algorithm
   */
  private async detectFraud(context: PaymentSecurityContext): Promise<FraudDetectionResult> {
    let riskScore = 0;
    const flags: string[] = [];
    const recommendations: string[] = [];

    // 1. Amount-based risk assessment
    if (context.amount > this.SUSPICIOUS_AMOUNT_THRESHOLD) {
      riskScore += 30;
      flags.push('High amount transaction');
      recommendations.push('Verify customer identity');
    }

    if (context.amount > this.MAX_PAYMENT_AMOUNT * 0.8) {
      riskScore += 20;
      flags.push('Near maximum amount limit');
    }

    // 2. Frequency-based risk assessment
    if (context.userId) {
      const userPayments = this.recentPayments.get(context.userId) || [];
      const recentPayments = userPayments.filter(
        timestamp => Date.now() - timestamp < this.RAPID_PAYMENT_THRESHOLD
      );

      if (recentPayments.length > 0) {
        riskScore += 25;
        flags.push('Rapid successive payments');
        recommendations.push('Implement payment cooling period');
      }

      const hourlyPayments = userPayments.filter(
        timestamp => Date.now() - timestamp < 3600000 // 1 hour
      );

      if (hourlyPayments.length >= this.MAX_PAYMENTS_PER_HOUR) {
        riskScore += 40;
        flags.push('Excessive payments per hour');
      }
    }

    // 3. IP-based risk assessment
    if (context.ipAddress) {
      if (this.suspiciousIPs.has(context.ipAddress)) {
        riskScore += 35;
        flags.push('Payment from flagged IP');
      }

      // Check for unusual geographic patterns (simplified)
      if (this.isUnusualGeographicPattern(context.ipAddress)) {
        riskScore += 15;
        flags.push('Unusual geographic location');
      }
    }

    // 4. User agent analysis
    if (context.userAgent) {
      if (this.isSuspiciousUserAgent(context.userAgent)) {
        riskScore += 20;
        flags.push('Suspicious user agent');
      }
    }

    // 5. Time-based analysis
    const hour = new Date().getHours();
    if (hour < 6 || hour > 23) { // Late night/early morning
      riskScore += 10;
      flags.push('Unusual time of transaction');
    }

    // 6. Session analysis
    if (!context.sessionId) {
      riskScore += 15;
      flags.push('Missing session information');
    }

    // Determine risk level
    let riskLevel: FraudDetectionResult['riskLevel'];
    if (riskScore >= 80) riskLevel = 'critical';
    else if (riskScore >= 60) riskLevel = 'high';
    else if (riskScore >= 30) riskLevel = 'medium';
    else riskLevel = 'low';

    // Determine if payment should be allowed
    const allowPayment = riskScore < 70; // Block if risk score >= 70

    return {
      riskScore,
      riskLevel,
      flags,
      recommendations,
      allowPayment
    };
  }

  /**
   * Validate payment amount
   */
  private validatePaymentAmount(amount: number): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!Number.isFinite(amount)) {
      errors.push('Invalid payment amount format');
    }

    if (amount < this.MIN_PAYMENT_AMOUNT) {
      errors.push(`Payment amount must be at least $${this.MIN_PAYMENT_AMOUNT}`);
    }

    if (amount > this.MAX_PAYMENT_AMOUNT) {
      errors.push(`Payment amount cannot exceed $${this.MAX_PAYMENT_AMOUNT}`);
    }

    // Check for suspicious decimal patterns
    const decimalPlaces = (amount.toString().split('.')[1] || '').length;
    if (decimalPlaces > 2) {
      errors.push('Invalid payment amount precision');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate payment rate limiting
   */
  private validatePaymentRateLimit(context: PaymentSecurityContext): { allowed: boolean; message: string } {
    if (!context.userId) {
      return { allowed: true, message: '' };
    }

    const userPayments = this.recentPayments.get(context.userId) || [];
    const now = Date.now();

    // Clean old payments
    const recentPayments = userPayments.filter(timestamp => now - timestamp < 86400000); // 24 hours

    // Check hourly limit
    const hourlyPayments = recentPayments.filter(timestamp => now - timestamp < 3600000);
    if (hourlyPayments.length >= this.MAX_PAYMENTS_PER_HOUR) {
      return {
        allowed: false,
        message: `Too many payments. Maximum ${this.MAX_PAYMENTS_PER_HOUR} payments per hour allowed.`
      };
    }

    // Check daily limit
    if (recentPayments.length >= this.MAX_PAYMENTS_PER_DAY) {
      return {
        allowed: false,
        message: `Daily payment limit reached. Maximum ${this.MAX_PAYMENTS_PER_DAY} payments per day allowed.`
      };
    }

    // Record this payment attempt
    recentPayments.push(now);
    this.recentPayments.set(context.userId, recentPayments);

    return { allowed: true, message: '' };
  }

  /**
   * Sanitize payment data
   */
  private sanitizePaymentData(data: any): any {
    const sanitized = { ...data };

    // Remove any potential script injections
    const dangerousFields = ['description', 'notes', 'customerName', 'address'];
    dangerousFields.forEach(field => {
      if (sanitized[field] && typeof sanitized[field] === 'string') {
        sanitized[field] = sanitized[field]
          .replace(/<script[^>]*>.*?<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '')
          .trim();
      }
    });

    // Ensure numeric fields are properly formatted
    if (sanitized.amount) {
      sanitized.amount = Math.round(parseFloat(sanitized.amount) * 100) / 100;
    }

    return sanitized;
  }

  /**
   * Validate payment data structure
   */
  private validatePaymentDataStructure(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields validation
    const requiredFields = ['amount'];
    requiredFields.forEach(field => {
      if (!data[field]) {
        errors.push(`Missing required field: ${field}`);
      }
    });

    // Data type validation
    if (data.amount && typeof data.amount !== 'number') {
      errors.push('Amount must be a number');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check for unusual geographic patterns (simplified implementation)
   */
  private isUnusualGeographicPattern(ipAddress: string): boolean {
    // In a real implementation, this would use IP geolocation services
    // For now, we'll do basic checks
    
    // Check for known VPN/proxy IP ranges (simplified)
    const suspiciousRanges = [
      '10.', '192.168.', '172.16.', // Private ranges used by some VPNs
      '127.', // Localhost
    ];

    return suspiciousRanges.some(range => ipAddress.startsWith(range));
  }

  /**
   * Check for suspicious user agents
   */
  private isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i,
      /^$/
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * Block a user from making payments
   */
  blockUser(userId: string, reason: string): void {
    this.blockedUsers.add(userId);
    
    BookingMonitor.trackSecurityEvent('unauthorized_access', {
      userId,
      details: { reason, action: 'user_blocked' }
    });
  }

  /**
   * Flag an IP address as suspicious
   */
  flagSuspiciousIP(ipAddress: string, reason: string): void {
    this.suspiciousIPs.add(ipAddress);
    
    BookingMonitor.trackSecurityEvent('suspicious_activity', {
      ipAddress,
      details: { reason, action: 'ip_flagged' }
    });
  }

  /**
   * Get payment security statistics
   */
  getSecurityStatistics(): {
    blockedUsers: number;
    suspiciousIPs: number;
    recentPayments: number;
    averageRiskScore: number;
  } {
    const totalRecentPayments = Array.from(this.recentPayments.values())
      .reduce((sum, payments) => sum + payments.length, 0);

    return {
      blockedUsers: this.blockedUsers.size,
      suspiciousIPs: this.suspiciousIPs.size,
      recentPayments: totalRecentPayments,
      averageRiskScore: 0 // Would be calculated from actual fraud detection results
    };
  }

  /**
   * Clear security data (for testing or maintenance)
   */
  clearSecurityData(): void {
    this.suspiciousIPs.clear();
    this.recentPayments.clear();
    this.blockedUsers.clear();
  }
}

// Export singleton instance
export const paymentSecurity = PaymentSecurity.getInstance();
