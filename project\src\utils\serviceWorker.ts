// Service Worker Registration and PWA Utilities

// Check if service workers are supported
export const isServiceWorkerSupported = (): boolean => {
  return 'serviceWorker' in navigator;
};

// Check if the app is running as a PWA
export const isPWA = (): boolean => {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true ||
         document.referrer.includes('android-app://');
};

// Register service worker
export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | null> => {
  if (!isServiceWorkerSupported()) {
    console.log('Service workers are not supported');
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/'
    });

    console.log('Service Worker registered successfully:', registration);

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New content is available, prompt user to refresh
            showUpdateAvailableNotification();
          }
        });
      }
    });

    return registration;
  } catch (error) {
    console.error('Service Worker registration failed:', error);
    return null;
  }
};

// Unregister service worker
export const unregisterServiceWorker = async (): Promise<boolean> => {
  if (!isServiceWorkerSupported()) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      console.log('Service Worker unregistered:', result);
      return result;
    }
    return false;
  } catch (error) {
    console.error('Service Worker unregistration failed:', error);
    return false;
  }
};

// Show update available notification
const showUpdateAvailableNotification = (): void => {
  // Create a custom notification or use a toast library
  const notification = document.createElement('div');
  notification.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: #3b82f6;
      color: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      max-width: 300px;
      font-family: system-ui, -apple-system, sans-serif;
    ">
      <div style="font-weight: 600; margin-bottom: 8px;">Update Available</div>
      <div style="font-size: 14px; margin-bottom: 12px;">A new version of the app is available.</div>
      <button onclick="window.location.reload()" style="
        background: white;
        color: #3b82f6;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: 600;
        cursor: pointer;
        margin-right: 8px;
      ">Update Now</button>
      <button onclick="this.parentElement.parentElement.remove()" style="
        background: transparent;
        color: white;
        border: 1px solid white;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
      ">Later</button>
    </div>
  `;
  
  document.body.appendChild(notification);
  
  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.remove();
    }
  }, 10000);
};

// Install prompt handling
let deferredPrompt: any = null;

// Listen for the beforeinstallprompt event
window.addEventListener('beforeinstallprompt', (e) => {
  console.log('beforeinstallprompt event fired');
  // Prevent the mini-infobar from appearing on mobile
  e.preventDefault();
  // Stash the event so it can be triggered later
  deferredPrompt = e;
  // Show install button or notification
  showInstallPrompt();
});

// Show install prompt
export const showInstallPrompt = (): void => {
  if (isPWA()) {
    return; // Already installed
  }

  const installBanner = document.createElement('div');
  installBanner.innerHTML = `
    <div id="install-banner" style="
      position: fixed;
      bottom: 20px;
      left: 20px;
      right: 20px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;
      padding: 16px;
      border-radius: 12px;
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: system-ui, -apple-system, sans-serif;
      animation: slideUp 0.3s ease-out;
    ">
      <div>
        <div style="font-weight: 600; margin-bottom: 4px;">Install Empire Pro Cleaning</div>
        <div style="font-size: 14px; opacity: 0.9;">Get the full app experience</div>
      </div>
      <div>
        <button id="install-button" style="
          background: white;
          color: #3b82f6;
          border: none;
          padding: 10px 20px;
          border-radius: 6px;
          font-weight: 600;
          cursor: pointer;
          margin-right: 8px;
          font-size: 14px;
        ">Install</button>
        <button id="dismiss-install" style="
          background: transparent;
          color: white;
          border: 1px solid rgba(255,255,255,0.3);
          padding: 10px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
        ">×</button>
      </div>
    </div>
    <style>
      @keyframes slideUp {
        from { transform: translateY(100%); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
    </style>
  `;
  
  document.body.appendChild(installBanner);
  
  // Handle install button click
  const installButton = document.getElementById('install-button');
  const dismissButton = document.getElementById('dismiss-install');
  
  installButton?.addEventListener('click', async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      console.log(`User response to the install prompt: ${outcome}`);
      deferredPrompt = null;
    }
    installBanner.remove();
  });
  
  dismissButton?.addEventListener('click', () => {
    installBanner.remove();
    // Store dismissal to avoid showing again for a while
    localStorage.setItem('installPromptDismissed', Date.now().toString());
  });
  
  // Auto-remove after 15 seconds
  setTimeout(() => {
    if (installBanner.parentElement) {
      installBanner.remove();
    }
  }, 15000);
};

// Check if install prompt should be shown
export const shouldShowInstallPrompt = (): boolean => {
  if (isPWA()) {
    return false;
  }
  
  const dismissed = localStorage.getItem('installPromptDismissed');
  if (dismissed) {
    const dismissedTime = parseInt(dismissed);
    const daysSinceDismissed = (Date.now() - dismissedTime) / (1000 * 60 * 60 * 24);
    return daysSinceDismissed > 7; // Show again after 7 days
  }
  
  return true;
};

// Initialize PWA features
export const initializePWA = async (): Promise<void> => {
  // Register service worker
  await registerServiceWorker();
  
  // Show install prompt if appropriate
  if (shouldShowInstallPrompt()) {
    // Delay showing install prompt
    setTimeout(() => {
      if (deferredPrompt) {
        showInstallPrompt();
      }
    }, 3000);
  }
  
  // Handle app state changes
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      // App became visible, check for updates
      navigator.serviceWorker?.getRegistration().then(registration => {
        registration?.update();
      });
    }
  });
};

// Network status utilities
export const getNetworkStatus = (): { online: boolean; effectiveType?: string } => {
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  
  return {
    online: navigator.onLine,
    effectiveType: connection?.effectiveType
  };
};

// Listen for network changes
export const onNetworkChange = (callback: (online: boolean) => void): (() => void) => {
  const handleOnline = () => callback(true);
  const handleOffline = () => callback(false);
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  // Return cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};