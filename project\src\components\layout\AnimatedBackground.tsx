import React from 'react';

interface AnimatedBackgroundProps {
  children: React.ReactNode;
  variant?: 'waves' | 'topographic-green';
}

export function AnimatedBackground({ children, variant = 'waves' }: AnimatedBackgroundProps) {
  const baseBackground = '#0b0b0b'; // default nearly-black background

  return (
    <div style={{
      position: 'relative',
      minHeight: '100vh',
      width: '100%',
      background: variant === 'topographic-green' ? '#0a0a0a' : baseBackground,
      overflowX: 'hidden' // Only prevent horizontal overflow
    }}>
      {variant === 'waves' && (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 0
      }}>
        {/* Waves positioned to fill the entire screen */}

        {/* Top Wave Set */}
        <div style={{
          position: 'absolute',
          width: '200%',
          height: '200%',
          left: '-50%',
          top: '-165%',
          borderRadius: '45%',
          background: 'rgba(0, 255, 153, 0.08)', // subtle green tint
          animation: 'wave-motion 25s linear infinite'
        }} />
        <div style={{
          position: 'absolute',
          width: '200%',
          height: '200%',
          left: '-50%',
          top: '-160%',
          borderRadius: '47%',
          background: 'rgba(0, 255, 153, 0.06)',
          animation: 'wave-motion 20s linear infinite reverse'
        }} />
        <div style={{
          position: 'absolute',
          width: '200%',
          height: '200%',
          left: '-50%',
          top: '-155%',
          borderRadius: '46%',
          background: 'rgba(0, 255, 153, 0.04)',
          animation: 'wave-motion 15s linear infinite'
        }} />

        {/* Bottom Wave Set */}
        <div style={{
          position: 'absolute',
          width: '200%',
          height: '200%',
          left: '-50%',
          bottom: '-165%',
          borderRadius: '45%',
          background: 'rgba(0, 255, 153, 0.05)',
          animation: 'wave-motion 22s linear infinite reverse'
        }} />
        <div style={{
          position: 'absolute',
          width: '200%',
          height: '200%',
          left: '-50%',
          bottom: '-160%',
          borderRadius: '46%',
          background: 'rgba(0, 255, 153, 0.03)',
          animation: 'wave-motion 18s linear infinite'
        }} />
      </div>
      )}

      {variant === 'topographic-green' && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 0
        }}>
          {/* Animated Topographic Video Pattern */}
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 1920 1080"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '120%',
              height: '120%',
              transform: 'translate(-10%, -10%)'
            }}
          >
            <defs>
              {/* White & Green Animated Gradients */}
              <radialGradient id="whiteGreen1" cx="60%" cy="40%">
                <stop offset="0%" stopColor="#ffffff" stopOpacity="0.25">
                  <animate attributeName="stop-opacity" values="0.25;0.4;0.25" dur="4s" repeatCount="indefinite" />
                </stop>
                <stop offset="50%" stopColor="#22c55e" stopOpacity="0.15">
                  <animate attributeName="stop-opacity" values="0.15;0.25;0.15" dur="5s" repeatCount="indefinite" />
                </stop>
                <stop offset="100%" stopColor="#16a34a" stopOpacity="0.08">
                  <animate attributeName="stop-opacity" values="0.08;0.15;0.08" dur="6s" repeatCount="indefinite" />
                </stop>
              </radialGradient>
              
              <radialGradient id="whiteGreen2" cx="70%" cy="30%">
                <stop offset="0%" stopColor="#f0fdf4" stopOpacity="0.3">
                  <animate attributeName="stop-opacity" values="0.3;0.5;0.3" dur="3.5s" repeatCount="indefinite" />
                </stop>
                <stop offset="50%" stopColor="#34d399" stopOpacity="0.18">
                  <animate attributeName="stop-opacity" values="0.18;0.3;0.18" dur="4.5s" repeatCount="indefinite" />
                </stop>
                <stop offset="100%" stopColor="#10b981" stopOpacity="0.10">
                  <animate attributeName="stop-opacity" values="0.10;0.18;0.10" dur="5.5s" repeatCount="indefinite" />
                </stop>
              </radialGradient>
              
              <radialGradient id="whiteGreen3" cx="50%" cy="50%">
                <stop offset="0%" stopColor="#ffffff" stopOpacity="0.35">
                  <animate attributeName="stop-opacity" values="0.35;0.55;0.35" dur="4.2s" repeatCount="indefinite" />
                </stop>
                <stop offset="50%" stopColor="#4ade80" stopOpacity="0.22">
                  <animate attributeName="stop-opacity" values="0.22;0.35;0.22" dur="3.8s" repeatCount="indefinite" />
                </stop>
                <stop offset="100%" stopColor="#22c55e" stopOpacity="0.12">
                  <animate attributeName="stop-opacity" values="0.12;0.22;0.12" dur="5.2s" repeatCount="indefinite" />
                </stop>
              </radialGradient>
              
              <radialGradient id="whiteGreen4" cx="80%" cy="20%">
                <stop offset="0%" stopColor="#ecfdf5" stopOpacity="0.28">
                  <animate attributeName="stop-opacity" values="0.28;0.45;0.28" dur="3.8s" repeatCount="indefinite" />
                </stop>
                <stop offset="50%" stopColor="#6ee7b7" stopOpacity="0.16">
                  <animate attributeName="stop-opacity" values="0.16;0.28;0.16" dur="4.8s" repeatCount="indefinite" />
                </stop>
                <stop offset="100%" stopColor="#34d399" stopOpacity="0.08">
                  <animate attributeName="stop-opacity" values="0.08;0.16;0.08" dur="6.2s" repeatCount="indefinite" />
                </stop>
              </radialGradient>
            </defs>

            {/* Animated Topographic Contour Layers */}
            
            {/* Layer 1 - Outermost with flowing animation */}
            <path 
              d="M 800 -200 Q 1200 100 1600 300 Q 1800 500 1600 700 Q 1400 900 1000 800 Q 600 700 400 500 Q 200 300 400 100 Q 600 -100 800 -200 Z"
              fill="url(#whiteGreen1)"
              stroke="#ffffff"
              strokeWidth="2"
              strokeOpacity="0.2"
            >
              <animateTransform
                attributeName="transform"
                type="scale"
                values="1;1.05;1"
                dur="8s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-opacity"
                values="0.2;0.4;0.2"
                dur="6s"
                repeatCount="indefinite"
              />
            </path>

            {/* Layer 2 with morphing animation */}
            <path 
              d="M 850 -150 Q 1150 150 1500 350 Q 1700 550 1500 750 Q 1300 950 950 850 Q 650 750 450 550 Q 250 350 450 150 Q 650 -50 850 -150 Z"
              fill="url(#whiteGreen2)"
              stroke="#22c55e"
              strokeWidth="1.5"
              strokeOpacity="0.25"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                values="0 960 540;5 960 540;0 960 540"
                dur="12s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-opacity"
                values="0.25;0.45;0.25"
                dur="7s"
                repeatCount="indefinite"
              />
            </path>

            {/* Layer 3 with pulsing animation */}
            <path 
              d="M 900 -100 Q 1100 200 1400 400 Q 1600 600 1400 800 Q 1200 1000 900 900 Q 700 800 500 600 Q 300 400 500 200 Q 700 0 900 -100 Z"
              fill="url(#whiteGreen3)"
              stroke="#ffffff"
              strokeWidth="2.5"
              strokeOpacity="0.3"
            >
              <animateTransform
                attributeName="transform"
                type="scale"
                values="1;0.95;1"
                dur="10s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-opacity"
                values="0.3;0.6;0.3"
                dur="5s"
                repeatCount="indefinite"
              />
            </path>

            {/* Layer 4 with sliding animation */}
            <path 
              d="M 950 -50 Q 1050 250 1300 450 Q 1500 650 1300 850 Q 1100 1050 850 950 Q 750 850 550 650 Q 350 450 550 250 Q 750 50 950 -50 Z"
              fill="url(#whiteGreen4)"
              stroke="#16a34a"
              strokeWidth="1.8"
              strokeOpacity="0.22"
            >
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0 0;10 -5;0 0"
                dur="9s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-opacity"
                values="0.22;0.4;0.22"
                dur="6.5s"
                repeatCount="indefinite"
              />
            </path>

            {/* Layer 5 - Inner flowing */}
            <path 
              d="M 1000 0 Q 1000 300 1200 500 Q 1400 700 1200 900 Q 1000 1100 800 1000 Q 800 900 600 700 Q 400 500 600 300 Q 800 100 1000 0 Z"
              fill="url(#whiteGreen1)"
              stroke="#ffffff"
              strokeWidth="2"
              strokeOpacity="0.35"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                values="0 960 540;-3 960 540;0 960 540"
                dur="14s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-opacity"
                values="0.35;0.6;0.35"
                dur="4.5s"
                repeatCount="indefinite"
              />
            </path>

            {/* Layer 6 - Core breathing */}
            <path 
              d="M 1050 50 Q 950 350 1100 550 Q 1300 750 1100 950 Q 950 1150 750 1050 Q 850 950 650 750 Q 450 550 650 350 Q 850 150 1050 50 Z"
              fill="url(#whiteGreen3)"
              stroke="#4ade80"
              strokeWidth="3"
              strokeOpacity="0.4"
            >
              <animateTransform
                attributeName="transform"
                type="scale"
                values="1;1.1;1"
                dur="6s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-opacity"
                values="0.4;0.7;0.4"
                dur="4s"
                repeatCount="indefinite"
              />
            </path>

            {/* Additional flowing animated contours */}
            <path 
              d="M 1400 200 Q 1600 400 1400 600 Q 1200 800 1000 600 Q 800 400 1000 200 Q 1200 0 1400 200 Z"
              fill="none"
              stroke="#ffffff"
              strokeWidth="3"
              strokeOpacity="0.15"
            >
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0 0;-15 10;0 0"
                dur="11s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-opacity"
                values="0.15;0.35;0.15"
                dur="5.5s"
                repeatCount="indefinite"
              />
            </path>

            <path 
              d="M 1300 300 Q 1500 500 1300 700 Q 1100 900 900 700 Q 700 500 900 300 Q 1100 100 1300 300 Z"
              fill="none"
              stroke="#22c55e"
              strokeWidth="2.5"
              strokeOpacity="0.18"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                values="0 960 540;8 960 540;0 960 540"
                dur="16s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="stroke-opacity"
                values="0.18;0.4;0.18"
                dur="7.5s"
                repeatCount="indefinite"
              />
            </path>

          </svg>
        </div>
      )}
      
      <div style={{ position: 'relative', zIndex: 1, width: '100%' }}>
        {children}
      </div>

      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes wave-motion {
          0%   { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}} />
    </div>
  );
} 