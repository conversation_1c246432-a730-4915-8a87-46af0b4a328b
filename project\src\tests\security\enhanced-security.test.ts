/**
 * Enhanced Security Tests
 * 
 * Comprehensive tests for the enhanced security modules including
 * payment security, session security, data encryption, and security headers.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { PaymentSecurity } from '../../lib/security/paymentSecurity';
import { SessionSecurity } from '../../lib/security/sessionSecurity';
import { DataEncryption } from '../../lib/security/dataEncryption';
import { SecurityHeaders } from '../../lib/security/securityHeaders';

// Mock BookingMonitor
vi.mock('../../lib/monitoring/bookingMonitoringIntegration', () => ({
  BookingMonitor: {
    trackUserActivity: vi.fn(),
    trackSecurityEvent: vi.fn(),
    trackError: vi.fn()
  }
}));

// Mock crypto for testing
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: (arr: Uint8Array) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    },
    subtle: {
      generateKey: vi.fn().mockResolvedValue({ type: 'secret', algorithm: { name: 'AES-GCM' } }),
      importKey: vi.fn().mockResolvedValue({ type: 'secret', algorithm: { name: 'PBKDF2' } }),
      deriveKey: vi.fn().mockResolvedValue({ type: 'secret', algorithm: { name: 'AES-GCM' } }),
      encrypt: vi.fn().mockImplementation(() => {
        const mockData = new Uint8Array(32);
        for (let i = 0; i < 32; i++) {
          mockData[i] = Math.floor(Math.random() * 256);
        }
        return Promise.resolve(mockData.buffer);
      }),
      decrypt: vi.fn().mockImplementation(() => {
        return Promise.resolve(new TextEncoder().encode('decrypted data'));
      }),
      digest: vi.fn().mockImplementation(() => {
        const mockHash = new Uint8Array(32);
        for (let i = 0; i < 32; i++) {
          mockHash[i] = Math.floor(Math.random() * 256);
        }
        return Promise.resolve(mockHash.buffer);
      })
    }
  }
});

// Mock btoa/atob for Node.js environment
global.btoa = (str: string) => Buffer.from(str, 'binary').toString('base64');
global.atob = (str: string) => Buffer.from(str, 'base64').toString('binary');

describe('Enhanced Security Systems', () => {
  
  describe('Payment Security', () => {
    let paymentSecurity: PaymentSecurity;

    beforeEach(() => {
      paymentSecurity = PaymentSecurity.getInstance();
      paymentSecurity.clearSecurityData();
    });

    it('should validate payment amounts correctly', async () => {
      const validContext = {
        userId: 'user-123',
        sessionId: 'session-456',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        amount: 150,
        currency: 'USD',
        timestamp: Date.now()
      };

      const result = await paymentSecurity.validatePaymentSecurity({}, validContext);

      // Debug the result
      if (!result.isValid) {
        console.log('Validation errors:', result.errors);
        console.log('Validation warnings:', result.warnings);
      }

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid payment amounts', async () => {
      const invalidContext = {
        userId: 'user-123',
        amount: -50, // Invalid negative amount
        currency: 'USD',
        timestamp: Date.now(),
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0'
      };

      const result = await paymentSecurity.validatePaymentSecurity({}, invalidContext);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('amount'))).toBe(true);
    });

    it('should detect fraud based on amount thresholds', async () => {
      const highAmountContext = {
        userId: 'user-123',
        amount: 8000, // High amount
        currency: 'USD',
        timestamp: Date.now(),
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0'
      };

      const result = await paymentSecurity.validatePaymentSecurity({}, highAmountContext);
      expect(result.warnings.length).toBeGreaterThan(0);
    });

    it('should enforce rate limiting', async () => {
      const context = {
        userId: 'user-123',
        amount: 100,
        currency: 'USD',
        timestamp: Date.now(),
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0'
      };

      // Make multiple payments to trigger rate limiting
      for (let i = 0; i < 6; i++) {
        await paymentSecurity.validatePaymentSecurity({}, context);
      }

      const result = await paymentSecurity.validatePaymentSecurity({}, context);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Too many payments'))).toBe(true);
    });

    it('should sanitize payment data', async () => {
      const maliciousData = {
        description: '<script>alert("xss")</script>',
        notes: 'javascript:void(0)',
        amount: '150.00'
      };

      const context = {
        userId: 'user-123',
        amount: 150,
        currency: 'USD',
        timestamp: Date.now(),
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0'
      };

      const result = await paymentSecurity.validatePaymentSecurity(maliciousData, context);
      expect(result.sanitizedData.description).not.toContain('<script>');
      expect(result.sanitizedData.notes).not.toContain('javascript:');
    });

    it('should block flagged users', async () => {
      const userId = 'blocked-user';
      paymentSecurity.blockUser(userId, 'Suspicious activity');

      const context = {
        userId,
        amount: 100,
        currency: 'USD',
        timestamp: Date.now(),
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0'
      };

      const result = await paymentSecurity.validatePaymentSecurity({}, context);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('blocked'))).toBe(true);
    });
  });

  describe('Session Security', () => {
    let sessionSecurity: SessionSecurity;

    beforeEach(() => {
      sessionSecurity = SessionSecurity.getInstance();
    });

    it('should create secure sessions', () => {
      const session = sessionSecurity.createSession(
        'user-123',
        '***********',
        'Mozilla/5.0',
        true
      );

      expect(session.sessionId).toBeDefined();
      expect(session.userId).toBe('user-123');
      expect(session.isAuthenticated).toBe(true);
      expect(session.csrfToken).toBeDefined();
      expect(session.fingerprint).toBeDefined();
    });

    it('should validate session consistency', () => {
      const session = sessionSecurity.createSession(
        'user-123',
        '***********',
        'Mozilla/5.0',
        true
      );

      // Valid session validation
      const validResult = sessionSecurity.validateSession(
        session.sessionId,
        '***********',
        'Mozilla/5.0',
        true
      );

      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);
    });

    it('should detect IP address changes', () => {
      const session = sessionSecurity.createSession(
        'user-123',
        '***********',
        'Mozilla/5.0',
        true
      );

      // Different IP address
      const result = sessionSecurity.validateSession(
        session.sessionId,
        '***********',
        'Mozilla/5.0',
        true
      );

      expect(result.warnings.some(w => w.includes('IP address changed'))).toBe(true);
      expect(result.securityFlags).toContain('IP_CHANGE');
    });

    it('should detect user agent changes', () => {
      const session = sessionSecurity.createSession(
        'user-123',
        '***********',
        'Mozilla/5.0',
        true
      );

      // Different user agent
      const result = sessionSecurity.validateSession(
        session.sessionId,
        '***********',
        'Chrome/91.0',
        true
      );

      expect(result.warnings.some(w => w.includes('User agent changed'))).toBe(true);
      expect(result.securityFlags).toContain('USER_AGENT_CHANGE');
      expect(result.requiresRotation).toBe(true);
    });

    it('should rotate sessions', () => {
      const originalSession = sessionSecurity.createSession(
        'user-123',
        '***********',
        'Mozilla/5.0',
        true
      );

      const rotatedSession = sessionSecurity.rotateSession(originalSession.sessionId);

      expect(rotatedSession).toBeDefined();
      expect(rotatedSession!.sessionId).not.toBe(originalSession.sessionId);
      expect(rotatedSession!.userId).toBe(originalSession.userId);
      expect(rotatedSession!.csrfToken).not.toBe(originalSession.csrfToken);
    });

    it('should enforce concurrent session limits', () => {
      const userId = 'user-123';
      const sessions: string[] = [];

      // Create multiple sessions
      for (let i = 0; i < 5; i++) {
        const session = sessionSecurity.createSession(
          userId,
          '***********',
          'Mozilla/5.0',
          true
        );
        sessions.push(session.sessionId);
      }

      // Check that old sessions were destroyed
      const userSessions = sessionSecurity.getUserSessions(userId);
      expect(userSessions.length).toBeLessThanOrEqual(3); // Max concurrent sessions
    });

    it('should destroy sessions', () => {
      const session = sessionSecurity.createSession(
        'user-123',
        '***********',
        'Mozilla/5.0',
        true
      );

      const destroyed = sessionSecurity.destroySession(session.sessionId);
      expect(destroyed).toBe(true);

      const retrievedSession = sessionSecurity.getSession(session.sessionId);
      expect(retrievedSession).toBeNull();
    });
  });

  describe('Data Encryption', () => {
    let dataEncryption: DataEncryption;

    beforeEach(async () => {
      dataEncryption = DataEncryption.getInstance();
      await dataEncryption.initialize('test-master-key-32-characters-long');
    });

    it('should encrypt and decrypt data', async () => {
      const originalData = 'sensitive information';
      
      const encrypted = await dataEncryption.encryptData(originalData, 'test');
      expect(encrypted.encryptedData).toBeDefined();
      expect(encrypted.iv).toBeDefined();
      expect(encrypted.algorithm).toBe('AES-GCM');

      const decrypted = await dataEncryption.decryptData(encrypted, 'test');
      expect(decrypted.isValid).toBe(true);
      expect(decrypted.decryptedData).toBe(originalData);
    });

    it('should encrypt objects with sensitive fields', async () => {
      const sensitiveObject = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'secret123',
        creditCard: '****************',
        normalField: 'not sensitive'
      };

      const encrypted = await dataEncryption.encryptObject(sensitiveObject, 'user');
      
      // Sensitive fields should be encrypted
      expect(encrypted.password._encrypted).toBe(true);
      expect(encrypted.creditCard._encrypted).toBe(true);
      
      // Non-sensitive fields should remain unchanged
      expect(encrypted.name).toBe('John Doe');
      expect(encrypted.normalField).toBe('not sensitive');
    });

    it('should decrypt objects with encrypted fields', async () => {
      const sensitiveObject = {
        name: 'John Doe',
        password: 'secret123',
        creditCard: '****************'
      };

      const encrypted = await dataEncryption.encryptObject(sensitiveObject, 'user');
      const decrypted = await dataEncryption.decryptObject(encrypted, 'user');

      expect(decrypted.name).toBe('John Doe');
      expect(decrypted.password).toBe('secret123');
      expect(decrypted.creditCard).toBe('****************');
    });

    it('should hash data securely', async () => {
      const data = 'password123';
      const hash1 = await dataEncryption.hashData(data);
      const hash2 = await dataEncryption.hashData(data);

      expect(hash1).toBeDefined();
      expect(hash1).toBe(hash2); // Same input should produce same hash
      expect(hash1).not.toBe(data); // Hash should be different from original
    });

    it('should generate secure tokens', () => {
      const token1 = dataEncryption.generateSecureToken(32);
      const token2 = dataEncryption.generateSecureToken(32);

      expect(token1).toBeDefined();
      expect(token2).toBeDefined();
      expect(token1).not.toBe(token2); // Should be unique
      expect(token1.length).toBeGreaterThan(0);
    });

    it('should mask sensitive data', () => {
      const email = '<EMAIL>';
      const phone = '(*************';
      const creditCard = '****************';

      const maskedEmail = dataEncryption.maskSensitiveData(email, 'email');
      const maskedPhone = dataEncryption.maskSensitiveData(phone, 'phone');
      const maskedCard = dataEncryption.maskSensitiveData(creditCard, 'creditCard');

      expect(maskedEmail).toContain('j***@example.com');
      expect(maskedPhone).toContain('***');
      expect(maskedCard).toContain('*');
      expect(maskedCard).not.toBe(creditCard);
    });
  });

  describe('Security Headers', () => {
    let securityHeaders: SecurityHeaders;

    beforeEach(() => {
      securityHeaders = SecurityHeaders.getInstance();
    });

    it('should generate production security headers', () => {
      const result = securityHeaders.generateHeaders('production');
      
      expect(result.headers['Content-Security-Policy']).toBeDefined();
      expect(result.headers['Strict-Transport-Security']).toBeDefined();
      expect(result.headers['X-Frame-Options']).toBeDefined();
      expect(result.headers['X-Content-Type-Options']).toBe('nosniff');
      expect(result.headers['Referrer-Policy']).toBeDefined();
    });

    it('should adjust headers for development environment', () => {
      const result = securityHeaders.generateHeaders('development');
      
      expect(result.headers['Content-Security-Policy-Report-Only']).toBeDefined();
      expect(result.headers['Strict-Transport-Security']).toBeUndefined();
      expect(result.warnings.some(w => w.includes('HSTS disabled'))).toBe(true);
    });

    it('should validate CSP directives', () => {
      const cspHeader = result.headers['Content-Security-Policy'] || result.headers['Content-Security-Policy-Report-Only'];
      
      expect(cspHeader).toContain("default-src 'self'");
      expect(cspHeader).toContain('https://js.squareup.com');
      expect(cspHeader).toContain('frame-ancestors');
    });

    it('should generate meta tags', () => {
      const metaTags = securityHeaders.generateMetaTags('production');
      
      expect(metaTags.length).toBeGreaterThan(0);
      expect(metaTags.some(tag => tag.includes('Content-Security-Policy'))).toBe(true);
      expect(metaTags.some(tag => tag.includes('X-Frame-Options'))).toBe(true);
    });

    it('should validate configuration', () => {
      const validation = securityHeaders.validateConfiguration();
      
      expect(validation.isValid).toBe(true);
      expect(Array.isArray(validation.errors)).toBe(true);
      expect(Array.isArray(validation.warnings)).toBe(true);
    });

    it('should update CSP directives', () => {
      securityHeaders.updateCSPDirective('script-src', ["'self'", 'https://example.com']);
      securityHeaders.addCSPSource('script-src', 'https://newdomain.com');
      
      const config = securityHeaders.getConfiguration();
      expect(config.contentSecurityPolicy.directives['script-src']).toContain('https://newdomain.com');
    });
  });

  describe('Integration Security Tests', () => {
    it('should handle complete security workflow', async () => {
      // Initialize all security modules
      const paymentSecurity = PaymentSecurity.getInstance();
      const sessionSecurity = SessionSecurity.getInstance();
      const dataEncryption = DataEncryption.getInstance();
      const securityHeaders = SecurityHeaders.getInstance();

      await dataEncryption.initialize('test-master-key');

      // Create secure session
      const session = sessionSecurity.createSession(
        'user-123',
        '***********',
        'Mozilla/5.0',
        true
      );

      // Validate payment with session context
      const paymentContext = {
        userId: session.userId!,
        sessionId: session.sessionId,
        ipAddress: session.ipAddress,
        userAgent: session.userAgent,
        amount: 150,
        currency: 'USD',
        timestamp: Date.now()
      };

      const paymentValidation = await paymentSecurity.validatePaymentSecurity({}, paymentContext);
      expect(paymentValidation.isValid).toBe(true);

      // Encrypt sensitive payment data
      const sensitiveData = {
        customerName: 'John Doe',
        creditCard: '****************',
        amount: 150
      };

      const encryptedData = await dataEncryption.encryptObject(sensitiveData, 'payment');
      expect(encryptedData.creditCard._encrypted).toBe(true);

      // Generate security headers
      const headers = securityHeaders.generateHeaders('production');
      expect(headers.headers['Content-Security-Policy']).toBeDefined();

      // Validate session
      const sessionValidation = sessionSecurity.validateSession(
        session.sessionId,
        session.ipAddress,
        session.userAgent,
        true
      );
      expect(sessionValidation.isValid).toBe(true);
    });
  });
});

// Helper to fix the missing result variable
function getSecurityHeaders() {
  const securityHeaders = SecurityHeaders.getInstance();
  return securityHeaders.generateHeaders('production');
}

const result = getSecurityHeaders();
