/**
 * Session Security Module
 * 
 * Comprehensive session management and security features including
 * session hijacking prevention, secure session storage, and session
 * lifecycle management.
 * 
 * CRITICAL: This module prevents session-based attacks including
 * session hijacking, fixation, and unauthorized access.
 */

import { BookingMonitor } from '../monitoring/bookingMonitoringIntegration';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface SessionData {
  sessionId: string;
  userId?: string;
  ipAddress: string;
  userAgent: string;
  createdAt: number;
  lastActivity: number;
  isAuthenticated: boolean;
  permissions: string[];
  fingerprint: string;
  csrfToken: string;
}

export interface SessionSecurityConfig {
  maxSessionAge: number; // milliseconds
  maxInactivityTime: number; // milliseconds
  requireSecureConnection: boolean;
  enableSessionRotation: boolean;
  enableFingerprintValidation: boolean;
  maxConcurrentSessions: number;
}

export interface SessionValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  requiresRotation: boolean;
  securityFlags: string[];
}

// ============================================================================
// Session Security Class
// ============================================================================

export class SessionSecurity {
  private static instance: SessionSecurity;
  private sessions: Map<string, SessionData> = new Map();
  private userSessions: Map<string, Set<string>> = new Map(); // userId -> sessionIds
  private suspiciousActivities: Map<string, number> = new Map(); // sessionId -> count

  private readonly config: SessionSecurityConfig = {
    maxSessionAge: 24 * 60 * 60 * 1000, // 24 hours
    maxInactivityTime: 2 * 60 * 60 * 1000, // 2 hours
    requireSecureConnection: true,
    enableSessionRotation: true,
    enableFingerprintValidation: true,
    maxConcurrentSessions: 3
  };

  private constructor() {
    // Start session cleanup interval
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  static getInstance(): SessionSecurity {
    if (!SessionSecurity.instance) {
      SessionSecurity.instance = new SessionSecurity();
    }
    return SessionSecurity.instance;
  }

  /**
   * Create a new secure session
   */
  createSession(
    userId?: string,
    ipAddress: string = '',
    userAgent: string = '',
    isAuthenticated: boolean = false
  ): SessionData {
    const sessionId = this.generateSecureSessionId();
    const fingerprint = this.generateFingerprint(ipAddress, userAgent);
    const csrfToken = this.generateCSRFToken();

    const sessionData: SessionData = {
      sessionId,
      userId,
      ipAddress,
      userAgent,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      isAuthenticated,
      permissions: [],
      fingerprint,
      csrfToken
    };

    // Check concurrent session limits
    if (userId && isAuthenticated) {
      this.enforceConcurrentSessionLimits(userId);
      
      // Track user sessions
      if (!this.userSessions.has(userId)) {
        this.userSessions.set(userId, new Set());
      }
      this.userSessions.get(userId)!.add(sessionId);
    }

    this.sessions.set(sessionId, sessionData);

    // Log session creation
    BookingMonitor.trackUserActivity('session_start', userId, sessionId, {
      ipAddress,
      userAgent: this.sanitizeUserAgent(userAgent),
      isAuthenticated
    });

    return sessionData;
  }

  /**
   * Validate session security
   */
  validateSession(
    sessionId: string,
    ipAddress: string,
    userAgent: string,
    isSecureConnection: boolean = true
  ): SessionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const securityFlags: string[] = [];
    let requiresRotation = false;

    const session = this.sessions.get(sessionId);
    if (!session) {
      errors.push('Session not found or expired');
      return {
        isValid: false,
        errors,
        warnings,
        requiresRotation: false,
        securityFlags: ['SESSION_NOT_FOUND']
      };
    }

    // 1. Check session expiration
    const now = Date.now();
    if (now - session.createdAt > this.config.maxSessionAge) {
      errors.push('Session expired due to age');
      securityFlags.push('SESSION_EXPIRED');
    }

    if (now - session.lastActivity > this.config.maxInactivityTime) {
      errors.push('Session expired due to inactivity');
      securityFlags.push('SESSION_INACTIVE');
    }

    // 2. Validate IP address consistency
    if (session.ipAddress !== ipAddress) {
      warnings.push('IP address changed during session');
      securityFlags.push('IP_CHANGE');
      
      // Flag as suspicious activity
      this.flagSuspiciousActivity(sessionId, 'IP address change');
      
      if (this.config.enableFingerprintValidation) {
        requiresRotation = true;
      }
    }

    // 3. Validate user agent consistency
    if (session.userAgent !== userAgent) {
      warnings.push('User agent changed during session');
      securityFlags.push('USER_AGENT_CHANGE');
      
      this.flagSuspiciousActivity(sessionId, 'User agent change');
      requiresRotation = true;
    }

    // 4. Validate fingerprint
    if (this.config.enableFingerprintValidation) {
      const currentFingerprint = this.generateFingerprint(ipAddress, userAgent);
      if (session.fingerprint !== currentFingerprint) {
        warnings.push('Session fingerprint mismatch');
        securityFlags.push('FINGERPRINT_MISMATCH');
        requiresRotation = true;
      }
    }

    // 5. Check secure connection requirement
    if (this.config.requireSecureConnection && !isSecureConnection) {
      errors.push('Secure connection required');
      securityFlags.push('INSECURE_CONNECTION');
    }

    // 6. Check for suspicious activity
    const suspiciousCount = this.suspiciousActivities.get(sessionId) || 0;
    if (suspiciousCount > 3) {
      errors.push('Session flagged for suspicious activity');
      securityFlags.push('SUSPICIOUS_ACTIVITY');
    }

    // 7. Check session rotation requirement
    if (this.config.enableSessionRotation) {
      const sessionAge = now - session.createdAt;
      if (sessionAge > this.config.maxSessionAge / 2) { // Rotate at 50% of max age
        requiresRotation = true;
      }
    }

    // Update last activity if session is valid
    if (errors.length === 0) {
      session.lastActivity = now;
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      requiresRotation,
      securityFlags
    };
  }

  /**
   * Rotate session (create new session ID while preserving data)
   */
  rotateSession(oldSessionId: string): SessionData | null {
    const oldSession = this.sessions.get(oldSessionId);
    if (!oldSession) {
      return null;
    }

    // Create new session with same data but new ID and CSRF token
    const newSessionId = this.generateSecureSessionId();
    const newCSRFToken = this.generateCSRFToken();

    const newSession: SessionData = {
      ...oldSession,
      sessionId: newSessionId,
      csrfToken: newCSRFToken,
      createdAt: Date.now(), // Reset creation time
      lastActivity: Date.now()
    };

    // Update session storage
    this.sessions.set(newSessionId, newSession);
    this.sessions.delete(oldSessionId);

    // Update user session tracking
    if (oldSession.userId) {
      const userSessions = this.userSessions.get(oldSession.userId);
      if (userSessions) {
        userSessions.delete(oldSessionId);
        userSessions.add(newSessionId);
      }
    }

    // Log session rotation
    BookingMonitor.trackUserActivity('session_rotate', oldSession.userId, newSessionId, {
      oldSessionId,
      reason: 'security_rotation'
    });

    return newSession;
  }

  /**
   * Destroy session
   */
  destroySession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    // Remove from user session tracking
    if (session.userId) {
      const userSessions = this.userSessions.get(session.userId);
      if (userSessions) {
        userSessions.delete(sessionId);
        if (userSessions.size === 0) {
          this.userSessions.delete(session.userId);
        }
      }
    }

    // Remove session data
    this.sessions.delete(sessionId);
    this.suspiciousActivities.delete(sessionId);

    // Log session destruction
    BookingMonitor.trackUserActivity('session_end', session.userId, sessionId, {
      duration: Date.now() - session.createdAt
    });

    return true;
  }

  /**
   * Get session data
   */
  getSession(sessionId: string): SessionData | null {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Update session permissions
   */
  updateSessionPermissions(sessionId: string, permissions: string[]): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    session.permissions = [...permissions];
    session.lastActivity = Date.now();

    return true;
  }

  /**
   * Check if session has permission
   */
  hasPermission(sessionId: string, permission: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session || !session.isAuthenticated) {
      return false;
    }

    return session.permissions.includes(permission) || session.permissions.includes('admin');
  }

  /**
   * Get all sessions for a user
   */
  getUserSessions(userId: string): SessionData[] {
    const sessionIds = this.userSessions.get(userId) || new Set();
    return Array.from(sessionIds)
      .map(id => this.sessions.get(id))
      .filter((session): session is SessionData => session !== undefined);
  }

  /**
   * Destroy all sessions for a user
   */
  destroyUserSessions(userId: string): number {
    const sessionIds = this.userSessions.get(userId) || new Set();
    let destroyedCount = 0;

    sessionIds.forEach(sessionId => {
      if (this.destroySession(sessionId)) {
        destroyedCount++;
      }
    });

    return destroyedCount;
  }

  /**
   * Generate secure session ID
   */
  private generateSecureSessionId(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Generate CSRF token
   */
  private generateCSRFToken(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Generate session fingerprint
   */
  private generateFingerprint(ipAddress: string, userAgent: string): string {
    const data = `${ipAddress}:${userAgent}:${Date.now()}`;
    return btoa(data).substring(0, 32);
  }

  /**
   * Sanitize user agent for logging
   */
  private sanitizeUserAgent(userAgent: string): string {
    return userAgent.substring(0, 200); // Limit length
  }

  /**
   * Flag suspicious activity
   */
  private flagSuspiciousActivity(sessionId: string, reason: string): void {
    const currentCount = this.suspiciousActivities.get(sessionId) || 0;
    this.suspiciousActivities.set(sessionId, currentCount + 1);

    const session = this.sessions.get(sessionId);
    BookingMonitor.trackSecurityEvent('suspicious_activity', {
      userId: session?.userId,
      sessionId,
      details: { reason, count: currentCount + 1 }
    });
  }

  /**
   * Enforce concurrent session limits
   */
  private enforceConcurrentSessionLimits(userId: string): void {
    const userSessions = this.userSessions.get(userId) || new Set();
    
    if (userSessions.size >= this.config.maxConcurrentSessions) {
      // Remove oldest sessions
      const sessions = Array.from(userSessions)
        .map(id => this.sessions.get(id))
        .filter((session): session is SessionData => session !== undefined)
        .sort((a, b) => a.lastActivity - b.lastActivity);

      const sessionsToRemove = sessions.slice(0, sessions.length - this.config.maxConcurrentSessions + 1);
      sessionsToRemove.forEach(session => {
        this.destroySession(session.sessionId);
      });
    }
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.sessions.entries()) {
      const isExpired = 
        (now - session.createdAt > this.config.maxSessionAge) ||
        (now - session.lastActivity > this.config.maxInactivityTime);

      if (isExpired) {
        this.destroySession(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      BookingMonitor.trackUserActivity('session_cleanup', undefined, undefined, {
        cleanedSessions: cleanedCount
      });
    }
  }

  /**
   * Get session statistics
   */
  getSessionStatistics(): {
    totalSessions: number;
    authenticatedSessions: number;
    suspiciousActivities: number;
    averageSessionAge: number;
  } {
    const now = Date.now();
    const sessions = Array.from(this.sessions.values());
    
    const authenticatedSessions = sessions.filter(s => s.isAuthenticated).length;
    const averageSessionAge = sessions.length > 0 
      ? sessions.reduce((sum, s) => sum + (now - s.createdAt), 0) / sessions.length 
      : 0;

    return {
      totalSessions: sessions.length,
      authenticatedSessions,
      suspiciousActivities: this.suspiciousActivities.size,
      averageSessionAge
    };
  }
}

// Export singleton instance
export const sessionSecurity = SessionSecurity.getInstance();
