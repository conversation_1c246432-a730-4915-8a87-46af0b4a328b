// Mobile Configuration and Optimization Settings

export interface MobileConfig {
  // Touch and Gesture Settings
  touch: {
    minTouchTarget: number; // Minimum touch target size in pixels
    swipeThreshold: number; // Minimum distance for swipe detection
    tapDelay: number; // Delay to prevent accidental taps
    longPressDelay: number; // Long press detection delay
    doubleTapDelay: number; // Double tap detection window
  };
  
  // Performance Settings
  performance: {
    enableVirtualization: boolean; // Enable list virtualization
    lazyLoadImages: boolean; // Enable lazy loading for images
    prefetchRoutes: boolean; // Prefetch route components
    enableServiceWorker: boolean; // Enable PWA service worker
    cacheStrategy: 'aggressive' | 'conservative' | 'minimal';
  };
  
  // UI/UX Settings
  ui: {
    enableHapticFeedback: boolean; // Enable haptic feedback where supported
    showInstallPrompt: boolean; // Show PWA install prompt
    enablePullToRefresh: boolean; // Enable pull-to-refresh
    enableSwipeNavigation: boolean; // Enable swipe navigation
    bottomNavigation: boolean; // Show bottom navigation on mobile
    adaptiveLayout: boolean; // Enable adaptive layouts
  };
  
  // Network Settings
  network: {
    enableOfflineMode: boolean; // Enable offline functionality
    retryAttempts: number; // Number of retry attempts for failed requests
    timeoutDuration: number; // Request timeout in milliseconds
    enableDataSaver: boolean; // Enable data saver mode
  };
  
  // Accessibility Settings
  accessibility: {
    enableHighContrast: boolean; // Support high contrast mode
    enableReducedMotion: boolean; // Respect reduced motion preferences
    enableScreenReader: boolean; // Optimize for screen readers
    minFontSize: number; // Minimum font size
    enableVoiceOver: boolean; // Enable VoiceOver optimizations
  };
  
  // Device-specific Settings
  device: {
    enableSafeArea: boolean; // Handle safe area insets
    enableStatusBarStyling: boolean; // Style status bar
    enableFullscreen: boolean; // Enable fullscreen mode
    enableOrientationLock: boolean; // Lock orientation
    preferredOrientation: 'portrait' | 'landscape' | 'auto';
  };
}

// Default mobile configuration
export const defaultMobileConfig: MobileConfig = {
  touch: {
    minTouchTarget: 44, // iOS HIG recommendation
    swipeThreshold: 50,
    tapDelay: 100,
    longPressDelay: 500,
    doubleTapDelay: 300
  },
  
  performance: {
    enableVirtualization: true,
    lazyLoadImages: true,
    prefetchRoutes: true,
    enableServiceWorker: true,
    cacheStrategy: 'conservative'
  },
  
  ui: {
    enableHapticFeedback: true,
    showInstallPrompt: true,
    enablePullToRefresh: true,
    enableSwipeNavigation: true,
    bottomNavigation: true,
    adaptiveLayout: true
  },
  
  network: {
    enableOfflineMode: true,
    retryAttempts: 3,
    timeoutDuration: 10000,
    enableDataSaver: false
  },
  
  accessibility: {
    enableHighContrast: true,
    enableReducedMotion: true,
    enableScreenReader: true,
    minFontSize: 16,
    enableVoiceOver: true
  },
  
  device: {
    enableSafeArea: true,
    enableStatusBarStyling: true,
    enableFullscreen: false,
    enableOrientationLock: false,
    preferredOrientation: 'auto'
  }
};

// Device-specific configurations
export const iosConfig: Partial<MobileConfig> = {
  touch: {
    minTouchTarget: 44, // iOS HIG standard
    tapDelay: 50, // iOS is more responsive
    longPressDelay: 500,
    doubleTapDelay: 300,
    swipeThreshold: 50
  },
  
  ui: {
    enableHapticFeedback: true,
    enablePullToRefresh: true,
    bottomNavigation: true,
    adaptiveLayout: true,
    enableSwipeNavigation: true,
    showInstallPrompt: true
  },
  
  device: {
    enableSafeArea: true,
    enableStatusBarStyling: true,
    enableFullscreen: false,
    enableOrientationLock: false,
    preferredOrientation: 'portrait'
  }
};

export const androidConfig: Partial<MobileConfig> = {
  touch: {
    minTouchTarget: 48, // Material Design standard
    tapDelay: 100,
    longPressDelay: 500,
    doubleTapDelay: 300,
    swipeThreshold: 50
  },
  
  ui: {
    enableHapticFeedback: true,
    enablePullToRefresh: true,
    bottomNavigation: true,
    adaptiveLayout: true,
    enableSwipeNavigation: true,
    showInstallPrompt: true
  },
  
  device: {
    enableSafeArea: true,
    enableStatusBarStyling: true,
    enableFullscreen: false,
    enableOrientationLock: false,
    preferredOrientation: 'portrait'
  }
};

// Tablet-specific configuration
export const tabletConfig: Partial<MobileConfig> = {
  touch: {
    minTouchTarget: 44,
    tapDelay: 100,
    longPressDelay: 500,
    doubleTapDelay: 300,
    swipeThreshold: 60
  },
  
  ui: {
    bottomNavigation: false, // Use side navigation on tablets
    adaptiveLayout: true,
    enableSwipeNavigation: true,
    enablePullToRefresh: true,
    enableHapticFeedback: false, // Less common on tablets
    showInstallPrompt: true
  },
  
  device: {
    enableSafeArea: false, // Less relevant on tablets
    enableStatusBarStyling: false,
    enableFullscreen: false,
    enableOrientationLock: false,
    preferredOrientation: 'auto'
  }
};

// Performance profiles
export const performanceProfiles = {
  high: {
    enableVirtualization: true,
    lazyLoadImages: true,
    prefetchRoutes: true,
    cacheStrategy: 'aggressive' as const,
    enableServiceWorker: true
  },
  
  medium: {
    enableVirtualization: true,
    lazyLoadImages: true,
    prefetchRoutes: false,
    cacheStrategy: 'conservative' as const,
    enableServiceWorker: true
  },
  
  low: {
    enableVirtualization: false,
    lazyLoadImages: false,
    prefetchRoutes: false,
    cacheStrategy: 'minimal' as const,
    enableServiceWorker: false
  }
};

// Utility functions
export const getMobileConfig = (deviceType?: string, performanceLevel?: keyof typeof performanceProfiles): MobileConfig => {
  let config = { ...defaultMobileConfig };
  
  // Apply device-specific config
  if (deviceType === 'ios') {
    config = { ...config, ...iosConfig };
  } else if (deviceType === 'android') {
    config = { ...config, ...androidConfig };
  } else if (deviceType === 'tablet') {
    config = { ...config, ...tabletConfig };
  }
  
  // Apply performance profile
  if (performanceLevel && performanceProfiles[performanceLevel]) {
    config.performance = { ...config.performance, ...performanceProfiles[performanceLevel] };
  }
  
  return config;
};

// Configuration validation
export const validateMobileConfig = (config: Partial<MobileConfig>): string[] => {
  const errors: string[] = [];
  
  if (config.touch?.minTouchTarget && config.touch.minTouchTarget < 32) {
    errors.push('Minimum touch target should be at least 32px for accessibility');
  }
  
  if (config.network?.timeoutDuration && config.network.timeoutDuration < 1000) {
    errors.push('Network timeout should be at least 1000ms');
  }
  
  if (config.accessibility?.minFontSize && config.accessibility.minFontSize < 12) {
    errors.push('Minimum font size should be at least 12px for readability');
  }
  
  return errors;
};

// Feature detection utilities
export const featureSupport = {
  hasTouch: () => 'ontouchstart' in window || navigator.maxTouchPoints > 0,
  hasHaptics: () => 'vibrate' in navigator,
  hasServiceWorker: () => 'serviceWorker' in navigator,
  hasNotifications: () => 'Notification' in window,
  hasGeolocation: () => 'geolocation' in navigator,
  hasCamera: () => 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
  hasWebGL: () => {
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch {
      return false;
    }
  },
  hasWebP: () => {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }
};

export default defaultMobileConfig;