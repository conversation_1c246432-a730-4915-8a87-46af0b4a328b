/**
 * Core Security Tests
 * 
 * Simplified tests focusing on core security functionality
 * without complex mocking requirements.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SecurityHeaders } from '../../lib/security/securityHeaders';

// Mock BookingMonitor
vi.mock('../../lib/monitoring/bookingMonitoringIntegration', () => ({
  BookingMonitor: {
    trackUserActivity: vi.fn(),
    trackSecurityEvent: vi.fn(),
    trackError: vi.fn()
  }
}));

describe('Core Security Systems', () => {
  
  describe('Security Headers', () => {
    let securityHeaders: SecurityHeaders;

    beforeEach(() => {
      securityHeaders = SecurityHeaders.getInstance();
    });

    it('should generate comprehensive security headers for production', () => {
      const result = securityHeaders.generateHeaders('production');
      
      expect(result.headers).toBeDefined();
      expect(result.headers['Content-Security-Policy']).toBeDefined();
      expect(result.headers['Strict-Transport-Security']).toBeDefined();
      expect(result.headers['X-Frame-Options']).toBe('DENY');
      expect(result.headers['X-Content-Type-Options']).toBe('nosniff');
      expect(result.headers['Referrer-Policy']).toBeDefined();
      expect(result.headers['X-XSS-Protection']).toBe('1; mode=block');
    });

    it('should include Square payment domains in CSP', () => {
      const result = securityHeaders.generateHeaders('production');
      const csp = result.headers['Content-Security-Policy'];
      
      expect(csp).toContain('https://js.squareup.com');
      expect(csp).toContain('https://js.squareupsandbox.com');
      expect(csp).toContain('https://connect.squareup.com');
    });

    it('should include Supabase domains in CSP', () => {
      const result = securityHeaders.generateHeaders('production');
      const csp = result.headers['Content-Security-Policy'];
      
      expect(csp).toContain('*.supabase.co');
      expect(csp).toContain('*.supabase.in');
    });

    it('should adjust headers for development environment', () => {
      const result = securityHeaders.generateHeaders('development');
      
      expect(result.headers['Content-Security-Policy-Report-Only']).toBeDefined();
      expect(result.headers['Strict-Transport-Security']).toBeUndefined();
      expect(result.warnings.some(w => w.includes('HSTS disabled'))).toBe(true);
    });

    it('should validate configuration correctly', () => {
      const validation = securityHeaders.validateConfiguration();
      
      expect(validation.isValid).toBe(true);
      expect(Array.isArray(validation.errors)).toBe(true);
      expect(Array.isArray(validation.warnings)).toBe(true);
    });

    it('should generate proper meta tags', () => {
      const metaTags = securityHeaders.generateMetaTags('production');
      
      expect(metaTags.length).toBeGreaterThan(0);
      expect(metaTags.some(tag => tag.includes('Content-Security-Policy'))).toBe(true);
      expect(metaTags.some(tag => tag.includes('X-Frame-Options'))).toBe(true);
    });

    it('should allow CSP directive updates', () => {
      securityHeaders.updateCSPDirective('script-src', ["'self'", 'https://example.com']);
      securityHeaders.addCSPSource('script-src', 'https://newdomain.com');
      
      const config = securityHeaders.getConfiguration();
      expect(config.contentSecurityPolicy.directives['script-src']).toContain('https://newdomain.com');
    });

    it('should include permissions policy', () => {
      const result = securityHeaders.generateHeaders('production');
      
      expect(result.headers['Permissions-Policy']).toBeDefined();
      expect(result.headers['Permissions-Policy']).toContain("camera=('none')");
      expect(result.headers['Permissions-Policy']).toContain("payment=('self' https://js.squareup.com https://js.squareupsandbox.com)");
    });

    it('should include cross-origin headers', () => {
      const result = securityHeaders.generateHeaders('production');
      
      expect(result.headers['Cross-Origin-Embedder-Policy']).toBe('unsafe-none');
      expect(result.headers['Cross-Origin-Opener-Policy']).toBe('same-origin-allow-popups');
      expect(result.headers['Cross-Origin-Resource-Policy']).toBe('cross-origin');
    });
  });

  describe('Security Configuration Validation', () => {
    it('should validate CSP configuration', () => {
      const securityHeaders = SecurityHeaders.getInstance();
      const config = securityHeaders.getConfiguration();
      
      // Check required CSP directives
      expect(config.contentSecurityPolicy.directives['default-src']).toBeDefined();
      expect(config.contentSecurityPolicy.directives['script-src']).toBeDefined();
      expect(config.contentSecurityPolicy.directives['style-src']).toBeDefined();
      expect(config.contentSecurityPolicy.directives['connect-src']).toBeDefined();
    });

    it('should validate HSTS configuration', () => {
      const securityHeaders = SecurityHeaders.getInstance();
      const config = securityHeaders.getConfiguration();
      
      expect(config.strictTransportSecurity.enabled).toBe(true);
      expect(config.strictTransportSecurity.maxAge).toBeGreaterThan(86400); // At least 1 day
      expect(config.strictTransportSecurity.includeSubDomains).toBe(true);
    });

    it('should validate frame options', () => {
      const securityHeaders = SecurityHeaders.getInstance();
      const config = securityHeaders.getConfiguration();
      
      expect(config.frameOptions.enabled).toBe(true);
      expect(['DENY', 'SAMEORIGIN', 'ALLOW-FROM']).toContain(config.frameOptions.policy);
    });
  });

  describe('Security Headers Integration', () => {
    it('should work with different environments', () => {
      const securityHeaders = SecurityHeaders.getInstance();
      
      const prodHeaders = securityHeaders.generateHeaders('production');
      const devHeaders = securityHeaders.generateHeaders('development');
      const stagingHeaders = securityHeaders.generateHeaders('staging');
      
      // Production should have strict CSP
      expect(prodHeaders.headers['Content-Security-Policy']).toBeDefined();
      
      // Development should have report-only CSP
      expect(devHeaders.headers['Content-Security-Policy-Report-Only']).toBeDefined();
      
      // Staging should have normal CSP
      expect(stagingHeaders.headers['Content-Security-Policy']).toBeDefined();
    });

    it('should maintain security while allowing necessary functionality', () => {
      const securityHeaders = SecurityHeaders.getInstance();
      const result = securityHeaders.generateHeaders('production');
      const csp = result.headers['Content-Security-Policy'];
      
      // Should allow Square payment processing
      expect(csp).toContain('https://js.squareup.com');
      expect(csp).toContain('frame-src');
      
      // Should allow Supabase API calls
      expect(csp).toContain('*.supabase.co');
      
      // Should block dangerous content
      expect(csp).toContain("object-src 'none'");
      expect(csp).toContain("frame-ancestors 'none'");
    });

    it('should provide comprehensive security coverage', () => {
      const securityHeaders = SecurityHeaders.getInstance();
      const result = securityHeaders.generateHeaders('production');
      
      // Check all major security headers are present
      const expectedHeaders = [
        'Content-Security-Policy',
        'Strict-Transport-Security',
        'X-Frame-Options',
        'X-Content-Type-Options',
        'Referrer-Policy',
        'Permissions-Policy',
        'X-XSS-Protection',
        'X-DNS-Prefetch-Control',
        'Cross-Origin-Embedder-Policy',
        'Cross-Origin-Opener-Policy'
      ];
      
      expectedHeaders.forEach(header => {
        expect(result.headers[header]).toBeDefined();
      });
    });
  });

  describe('Security Best Practices', () => {
    it('should implement defense in depth', () => {
      const securityHeaders = SecurityHeaders.getInstance();
      const result = securityHeaders.generateHeaders('production');
      
      // Multiple layers of XSS protection
      expect(result.headers['Content-Security-Policy']).toBeDefined();
      expect(result.headers['X-XSS-Protection']).toBeDefined();
      expect(result.headers['X-Content-Type-Options']).toBe('nosniff');
      
      // Multiple layers of clickjacking protection
      expect(result.headers['X-Frame-Options']).toBeDefined();
      expect(result.headers['Content-Security-Policy']).toContain('frame-ancestors');
    });

    it('should follow security header best practices', () => {
      const securityHeaders = SecurityHeaders.getInstance();
      const result = securityHeaders.generateHeaders('production');
      
      // HSTS should be long-term
      const hsts = result.headers['Strict-Transport-Security'];
      expect(hsts).toContain('max-age=31536000'); // 1 year
      expect(hsts).toContain('includeSubDomains');
      expect(hsts).toContain('preload');
      
      // Referrer policy should be restrictive
      expect(result.headers['Referrer-Policy']).toBe('strict-origin-when-cross-origin');
      
      // Frame options should be restrictive
      expect(result.headers['X-Frame-Options']).toBe('DENY');
    });

    it('should balance security with functionality', () => {
      const securityHeaders = SecurityHeaders.getInstance();
      const result = securityHeaders.generateHeaders('production');
      const csp = result.headers['Content-Security-Policy'];
      
      // Should allow necessary unsafe operations for React/payment processing
      expect(csp).toContain("'unsafe-inline'"); // Required for styled-components
      
      // But should still maintain security boundaries
      expect(csp).toContain("default-src 'self'");
      expect(csp).toContain("object-src 'none'");
      
      // Should allow payment processing while maintaining security
      expect(csp).toContain('https://js.squareup.com');
      expect(result.headers['Cross-Origin-Opener-Policy']).toBe('same-origin-allow-popups');
    });
  });
});
