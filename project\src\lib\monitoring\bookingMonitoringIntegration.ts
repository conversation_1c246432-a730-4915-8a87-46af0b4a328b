/**
 * Booking System Monitoring Integration
 * 
 * Integrates production monitoring throughout the booking system
 * to track user journeys, performance, and business metrics.
 */

import { ProductionMonitor, monitorPerformance } from './productionMonitor';

// ============================================================================
// Booking Journey Monitoring
// ============================================================================

export class BookingMonitoringIntegration {
  private static monitor = ProductionMonitor.getInstance();

  /**
   * Track complete booking journey
   */
  static trackBookingJourney(
    stage: 'form_start' | 'form_validation' | 'payment_initiation' | 'payment_completion' | 'booking_confirmation',
    data: {
      userId?: string;
      sessionId?: string;
      bookingId?: string;
      serviceType?: string;
      amount?: number;
      duration?: number;
      success?: boolean;
      errorMessage?: string;
      formData?: any;
    }
  ): void {
    const { userId, sessionId, bookingId, serviceType, amount, duration, success, errorMessage, formData } = data;

    // Track the specific stage
    this.monitor.trackBookingEvent(
      stage === 'form_start' ? 'started' :
      stage === 'form_validation' ? 'form_completed' :
      stage === 'payment_initiation' ? 'payment_initiated' :
      stage === 'payment_completion' ? 'payment_completed' :
      'booking_confirmed',
      bookingId,
      userId,
      {
        stage,
        serviceType,
        amount,
        duration,
        success,
        errorMessage,
        sessionId,
        formDataSize: formData ? JSON.stringify(formData).length : 0
      }
    );

    // Track performance if duration is provided
    if (duration !== undefined) {
      this.monitor.trackPerformance(
        `booking_${stage}`,
        duration,
        success !== false,
        {
          serviceType,
          userId,
          bookingId,
          errorMessage
        }
      );
    }

    // Log significant events
    if (stage === 'booking_confirmation' || errorMessage) {
      this.monitor.log(
        errorMessage ? 'error' : 'info',
        'business',
        `Booking ${stage}: ${success ? 'Success' : 'Failed'}`,
        {
          stage,
          bookingId,
          userId,
          serviceType,
          amount,
          errorMessage,
          sessionId
        },
        userId,
        sessionId
      );
    }
  }

  /**
   * Track form interactions and validation
   */
  static trackFormInteraction(
    action: 'field_focus' | 'field_blur' | 'field_change' | 'validation_error' | 'form_submit',
    fieldName?: string,
    data?: {
      userId?: string;
      sessionId?: string;
      serviceType?: string;
      validationErrors?: string[];
      fieldValue?: any;
      timeSpent?: number;
    }
  ): void {
    this.monitor.trackUserActivity(
      `form_${action}`,
      data?.userId,
      data?.sessionId,
      {
        action,
        fieldName,
        serviceType: data?.serviceType,
        validationErrors: data?.validationErrors,
        fieldValueType: typeof data?.fieldValue,
        timeSpent: data?.timeSpent
      }
    );

    // Track validation errors specifically
    if (action === 'validation_error' && data?.validationErrors) {
      data.validationErrors.forEach(error => {
        this.monitor.recordMetric(
          'form.validation_error',
          1,
          'count',
          {
            field: fieldName || 'unknown',
            error_type: error,
            service_type: data.serviceType || 'unknown'
          }
        );
      });
    }
  }

  /**
   * Track payment processing events
   */
  static trackPaymentProcessing(
    stage: 'link_creation' | 'redirect' | 'webhook_received' | 'status_update' | 'completion',
    data: {
      paymentId?: string;
      bookingId?: string;
      amount?: number;
      currency?: string;
      status?: string;
      duration?: number;
      errorMessage?: string;
      squarePaymentId?: string;
      webhookEventType?: string;
    }
  ): void {
    const { paymentId, bookingId, amount, currency, status, duration, errorMessage, squarePaymentId, webhookEventType } = data;

    // Track payment event
    if (paymentId) {
      this.monitor.trackPaymentEvent(
        stage === 'completion' ? (status === 'completed' ? 'completed' : 'failed') :
        stage === 'link_creation' ? 'initiated' :
        'processing',
        paymentId,
        amount,
        currency,
        {
          stage,
          bookingId,
          status,
          duration,
          errorMessage,
          squarePaymentId,
          webhookEventType
        }
      );
    }

    // Track performance
    if (duration !== undefined) {
      this.monitor.trackPerformance(
        `payment_${stage}`,
        duration,
        !errorMessage,
        {
          paymentId,
          bookingId,
          amount,
          status,
          errorMessage
        }
      );
    }

    // Log important events
    if (stage === 'completion' || errorMessage) {
      this.monitor.log(
        errorMessage ? 'error' : 'info',
        'business',
        `Payment ${stage}: ${errorMessage ? 'Failed' : 'Success'}`,
        {
          stage,
          paymentId,
          bookingId,
          amount,
          currency,
          status,
          errorMessage,
          squarePaymentId,
          webhookEventType
        }
      );
    }
  }

  /**
   * Track system performance metrics
   */
  static trackSystemPerformance(
    operation: string,
    startTime: number,
    success: boolean,
    metadata?: Record<string, any>
  ): void {
    const duration = Date.now() - startTime;
    
    this.monitor.trackPerformance(
      operation,
      duration,
      success,
      metadata
    );

    // Alert on slow operations
    if (duration > 5000) {
      this.monitor.log(
        'warn',
        'performance',
        `Slow operation detected: ${operation}`,
        {
          operation,
          duration,
          success,
          ...metadata
        }
      );
    }
  }

  /**
   * Track user behavior and engagement
   */
  static trackUserBehavior(
    action: 'page_view' | 'button_click' | 'form_abandon' | 'session_start' | 'session_end',
    data?: {
      userId?: string;
      sessionId?: string;
      page?: string;
      buttonId?: string;
      timeOnPage?: number;
      sessionDuration?: number;
      deviceType?: string;
      userAgent?: string;
    }
  ): void {
    this.monitor.trackUserActivity(
      action,
      data?.userId,
      data?.sessionId,
      {
        action,
        page: data?.page,
        buttonId: data?.buttonId,
        timeOnPage: data?.timeOnPage,
        sessionDuration: data?.sessionDuration,
        deviceType: data?.deviceType,
        userAgent: data?.userAgent
      }
    );

    // Track conversion funnel metrics
    if (action === 'form_abandon') {
      this.monitor.recordMetric(
        'conversion.form_abandon',
        1,
        'count',
        {
          page: data?.page || 'unknown',
          time_on_page: data?.timeOnPage?.toString() || 'unknown'
        }
      );
    }
  }

  /**
   * Track business metrics and KPIs
   */
  static trackBusinessMetrics(
    metric: 'booking_conversion' | 'payment_conversion' | 'average_booking_value' | 'customer_acquisition',
    value: number,
    metadata?: Record<string, any>
  ): void {
    this.monitor.recordMetric(
      `business.${metric}`,
      value,
      metric.includes('value') ? 'usd' : 'count',
      {
        metric,
        ...metadata
      }
    );

    this.monitor.log(
      'info',
      'business',
      `Business metric: ${metric} = ${value}`,
      {
        metric,
        value,
        ...metadata
      }
    );
  }

  /**
   * Track security events
   */
  static trackSecurityEvent(
    event: 'suspicious_activity' | 'failed_validation' | 'rate_limit_exceeded' | 'unauthorized_access',
    data?: {
      userId?: string;
      ipAddress?: string;
      userAgent?: string;
      details?: Record<string, any>;
    }
  ): void {
    this.monitor.log(
      'warn',
      'security',
      `Security event: ${event}`,
      {
        event,
        userId: data?.userId,
        ipAddress: data?.ipAddress,
        userAgent: data?.userAgent,
        ...data?.details
      },
      data?.userId
    );

    this.monitor.recordMetric(
      `security.${event}`,
      1,
      'count',
      {
        event,
        user_id: data?.userId || 'anonymous'
      }
    );
  }

  /**
   * Track error events with context
   */
  static trackError(
    error: Error | string,
    context: {
      operation?: string;
      userId?: string;
      sessionId?: string;
      bookingId?: string;
      paymentId?: string;
      severity?: 'low' | 'medium' | 'high' | 'critical';
      metadata?: Record<string, any>;
    }
  ): void {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const stack = typeof error === 'object' ? error.stack : undefined;

    this.monitor.log(
      context.severity === 'critical' ? 'critical' : 'error',
      'system',
      `Error in ${context.operation || 'unknown operation'}: ${errorMessage}`,
      {
        error: errorMessage,
        stack,
        operation: context.operation,
        bookingId: context.bookingId,
        paymentId: context.paymentId,
        ...context.metadata
      },
      context.userId,
      context.sessionId
    );

    this.monitor.recordMetric(
      'system.error',
      1,
      'count',
      {
        operation: context.operation || 'unknown',
        severity: context.severity || 'medium',
        error_type: errorMessage.split(':')[0] || 'unknown'
      }
    );
  }
}

// ============================================================================
// Monitoring Decorators and Utilities
// ============================================================================

/**
 * Decorator for automatic booking operation monitoring
 */
export function monitorBookingOperation(operationName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      let success = true;
      let error: any = null;

      try {
        const result = await method.apply(this, args);
        return result;
      } catch (err) {
        success = false;
        error = err;
        
        // Track the error
        BookingMonitoringIntegration.trackError(err, {
          operation: operationName,
          severity: 'high'
        });
        
        throw err;
      } finally {
        // Track performance
        BookingMonitoringIntegration.trackSystemPerformance(
          operationName,
          startTime,
          success,
          {
            method: propertyName,
            args: args.length,
            error: error?.message
          }
        );
      }
    };

    return descriptor;
  };
}

/**
 * Initialize monitoring for the booking system
 */
export async function initializeBookingMonitoring(): Promise<void> {
  const monitor = ProductionMonitor.getInstance();
  await monitor.initialize();
  
  // Track application startup
  BookingMonitoringIntegration.trackUserBehavior('session_start', {
    deviceType: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
    userAgent: navigator.userAgent
  });
  
  // Setup page visibility tracking
  if (typeof document !== 'undefined') {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        BookingMonitoringIntegration.trackUserBehavior('session_end');
      } else {
        BookingMonitoringIntegration.trackUserBehavior('session_start');
      }
    });
  }
}

export { BookingMonitoringIntegration as BookingMonitor };
