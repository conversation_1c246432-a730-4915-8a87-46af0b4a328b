/**
 * Comprehensive Test Helpers and Utilities
 * 
 * This module provides enhanced testing utilities to address gaps in
 * test coverage, mock configuration, and test data management.
 */

import { vi } from 'vitest';
import type { User } from '@supabase/supabase-js';

// ============================================================================
// Test Data Factories
// ============================================================================

export const TestDataFactory = {
  /**
   * Create mock user data
   */
  createMockUser(overrides: Partial<User> = {}): User {
    return {
      id: 'test-user-123',
      email: '<EMAIL>',
      phone: '+1234567890',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      email_confirmed_at: '2024-01-01T00:00:00.000Z',
      last_sign_in_at: '2024-01-01T00:00:00.000Z',
      app_metadata: {},
      user_metadata: {},
      aud: 'authenticated',
      confirmation_sent_at: '2024-01-01T00:00:00.000Z',
      recovery_sent_at: '2024-01-01T00:00:00.000Z',
      email_change_sent_at: '2024-01-01T00:00:00.000Z',
      new_email: null,
      invited_at: null,
      action_link: null,
      email_change: null,
      phone_change: null,
      phone_change_sent_at: null,
      phone_confirmed_at: null,
      confirmed_at: '2024-01-01T00:00:00.000Z',
      email_change_confirm_status: 0,
      phone_change_confirm_status: 0,
      banned_until: null,
      deleted_at: null,
      is_anonymous: false,
      ...overrides
    };
  },

  /**
   * Create mock residential form data
   */
  createMockResidentialFormData(overrides: any = {}) {
    return {
      serviceType: 'residential_regular',
      propertyDetails: {
        propertyType: 'house',
        propertySize: 'medium',
        bedrooms: 3,
        bathrooms: 2,
        squareFootage: 2000,
        propertyAddress: '123 Test St',
        city: 'Test City',
        state: 'CA',
        zipCode: '12345'
      },
      servicePreferences: {
        frequency: 'weekly',
        preferredTime: 'morning',
        cleaningFocus: ['kitchen', 'bathrooms'],
        specialInstructions: 'Please be careful with antique furniture'
      },
      schedule: {
        date: '2025-12-25',
        timeSlot: 'morning',
        flexibleScheduling: false
      },
      contact: {
        fullName: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '(*************',
        preferredContact: 'email'
      },
      totalPrice: 150,
      ...overrides
    };
  },

  /**
   * Create mock commercial form data
   */
  createMockCommercialFormData(overrides: any = {}) {
    return {
      serviceType: 'office',
      propertyDetails: {
        propertyType: 'office',
        industryType: 'technology',
        squareFootage: 5000,
        propertyAddress: '456 Business Ave',
        city: 'Business City',
        state: 'CA',
        zipCode: '54321'
      },
      serviceDetails: {
        serviceFrequency: 'weekly',
        cleaningAreas: ['offices', 'conference_rooms', 'restrooms'],
        specialRequirements: ['after_hours_only']
      },
      schedule: {
        date: '2025-12-25',
        timeSlot: 'evening',
        projectDeadline: '2025-12-30'
      },
      contact: {
        companyName: 'Test Corp',
        contactName: 'Jane Smith',
        email: '<EMAIL>',
        phone: '(*************',
        role: 'Office Manager',
        preferredContact: 'phone'
      },
      totalPrice: 300,
      ...overrides
    };
  },

  /**
   * Create mock payment response
   */
  createMockPaymentResponse(overrides: any = {}) {
    return {
      paymentLink: {
        id: 'payment-link-123',
        url: 'https://square.link/payment/test-123',
        order_id: 'order-123'
      },
      paymentRecordId: 'payment-record-123',
      bookingId: 'booking-123',
      ...overrides
    };
  },

  /**
   * Create mock booking record
   */
  createMockBookingRecord(overrides: any = {}) {
    return {
      id: 'booking-123',
      user_id: 'test-user-123',
      service_type: 'residential_regular',
      status: 'pending',
      payment_status: 'pending',
      property_details: {
        propertyType: 'house',
        squareFootage: 2000
      },
      service_details: {
        frequency: 'weekly'
      },
      schedule: {
        date: '2025-12-25',
        timeSlot: 'morning'
      },
      contact: {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '(*************'
      },
      total_price: 150,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      ...overrides
    };
  },

  /**
   * Create mock payment record
   */
  createMockPaymentRecord(overrides: any = {}) {
    return {
      id: 'payment-record-123',
      booking_id: 'booking-123',
      payment_link_id: 'payment-link-123',
      square_payment_id: 'square-payment-123',
      amount: 15000, // in cents
      status: 'pending',
      metadata: {},
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      ...overrides
    };
  }
};

// ============================================================================
// Mock Factories
// ============================================================================

export const MockFactory = {
  /**
   * Create comprehensive Supabase mock
   */
  createSupabaseMock() {
    const mockQuery = {
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      neq: vi.fn().mockReturnThis(),
      gt: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      lt: vi.fn().mockReturnThis(),
      lte: vi.fn().mockReturnThis(),
      like: vi.fn().mockReturnThis(),
      ilike: vi.fn().mockReturnThis(),
      is: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      contains: vi.fn().mockReturnThis(),
      containedBy: vi.fn().mockReturnThis(),
      rangeGt: vi.fn().mockReturnThis(),
      rangeGte: vi.fn().mockReturnThis(),
      rangeLt: vi.fn().mockReturnThis(),
      rangeLte: vi.fn().mockReturnThis(),
      rangeAdjacent: vi.fn().mockReturnThis(),
      overlaps: vi.fn().mockReturnThis(),
      textSearch: vi.fn().mockReturnThis(),
      match: vi.fn().mockReturnThis(),
      not: vi.fn().mockReturnThis(),
      or: vi.fn().mockReturnThis(),
      filter: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
      abortSignal: vi.fn().mockReturnThis(),
      single: vi.fn().mockImplementation(() => {
        // Return different data based on the table being queried
        return Promise.resolve({
          data: {
            id: 'mock-id',
            url: 'https://square.link/payment/mock-payment-link',
            square_payment_link_id: 'payment-link-123',
            square_payment_id: 'square-payment-123',
            status: 'pending',
            amount: 15000,
            ...TestDataFactory.createMockBookingRecord()
          },
          error: null
        });
      }),
      maybeSingle: vi.fn().mockImplementation(() => {
        return Promise.resolve({
          data: {
            id: 'mock-id',
            url: 'https://square.link/payment/mock-payment-link',
            square_payment_link_id: 'payment-link-123',
            ...TestDataFactory.createMockBookingRecord()
          },
          error: null
        });
      }),
      csv: vi.fn().mockResolvedValue({ data: '', error: null }),
      geojson: vi.fn().mockResolvedValue({ data: null, error: null }),
      explain: vi.fn().mockResolvedValue({ data: null, error: null }),
      rollback: vi.fn().mockResolvedValue({ data: null, error: null }),
      returns: vi.fn().mockReturnThis()
    };

    return {
      from: vi.fn().mockReturnValue(mockQuery),
      functions: {
        invoke: vi.fn().mockResolvedValue({
          data: TestDataFactory.createMockPaymentResponse(),
          error: null
        })
      },
      auth: {
        getUser: vi.fn().mockResolvedValue({
          data: { user: TestDataFactory.createMockUser() },
          error: null
        }),
        getSession: vi.fn().mockResolvedValue({ data: { session: null }, error: null }),
        signInWithPassword: vi.fn().mockResolvedValue({ data: { user: null, session: null }, error: null }),
        signUp: vi.fn().mockResolvedValue({ data: { user: null, session: null }, error: null }),
        signOut: vi.fn().mockResolvedValue({ error: null }),
        onAuthStateChange: vi.fn().mockReturnValue({ data: { subscription: { unsubscribe: vi.fn() } } })
      },
      storage: {
        from: vi.fn(() => ({
          upload: vi.fn().mockResolvedValue({ data: null, error: null }),
          download: vi.fn().mockResolvedValue({ data: null, error: null }),
          remove: vi.fn().mockResolvedValue({ data: null, error: null }),
          list: vi.fn().mockResolvedValue({ data: [], error: null }),
          getPublicUrl: vi.fn().mockReturnValue({ data: { publicUrl: '' } })
        }))
      }
    };
  },

  /**
   * Create Square API mock
   */
  createSquareMock() {
    return {
      paymentsApi: {
        createPayment: vi.fn().mockResolvedValue({
          result: {
            payment: {
              id: 'square-payment-123',
              status: 'COMPLETED',
              amountMoney: { amount: 15000, currency: 'USD' }
            }
          }
        })
      },
      checkoutApi: {
        createPaymentLink: vi.fn().mockResolvedValue({
          result: {
            paymentLink: {
              id: 'payment-link-123',
              url: 'https://square.link/payment/test-123',
              orderId: 'order-123'
            }
          }
        })
      }
    };
  },

  /**
   * Create environment mock
   */
  createEnvironmentMock() {
    return {
      VITE_SUPABASE_URL: 'https://test-project.supabase.co',
      VITE_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.key',
      VITE_SQUARE_APPLICATION_ID: 'sandbox-sq0idb-test-app-id',
      VITE_SQUARE_ACCESS_TOKEN: 'EAAAl-test-access-token',
      VITE_SQUARE_LOCATION_ID: 'test-location-id',
      VITE_SQUARE_ENVIRONMENT: 'sandbox',
      VITE_N8N_WEBHOOK_URL: 'https://test.n8n.cloud/webhook/test',
      PROD: false,
      DEV: true,
      MODE: 'test'
    };
  }
};

// ============================================================================
// Test Utilities
// ============================================================================

export const TestUtils = {
  /**
   * Wait for a specified amount of time
   */
  async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Create a mock function with call tracking
   */
  createTrackedMock<T extends (...args: any[]) => any>(implementation?: T) {
    const mock = vi.fn(implementation);
    const tracker = {
      calls: mock.mock.calls,
      results: mock.mock.results,
      instances: mock.mock.instances,
      lastCall: () => mock.mock.calls[mock.mock.calls.length - 1],
      lastResult: () => mock.mock.results[mock.mock.results.length - 1],
      callCount: () => mock.mock.calls.length,
      wasCalledWith: (...args: any[]) => mock.mock.calls.some(call => 
        call.length === args.length && call.every((arg, i) => arg === args[i])
      ),
      reset: () => mock.mockReset(),
      clear: () => mock.mockClear()
    };
    
    return { mock, tracker };
  },

  /**
   * Generate random test data
   */
  generateRandomData: {
    email: () => `test${Math.random().toString(36).substr(2, 9)}@example.com`,
    phone: () => `(555) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
    name: () => `Test User ${Math.random().toString(36).substr(2, 5)}`,
    address: () => `${Math.floor(Math.random() * 9999) + 1} Test St`,
    zipCode: () => Math.floor(Math.random() * 90000) + 10000,
    amount: () => Math.floor(Math.random() * 500) + 50,
    id: () => `test-${Math.random().toString(36).substr(2, 9)}`
  },

  /**
   * Validate test environment
   */
  validateTestEnvironment(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];
    
    // Check if we're in test mode
    if (import.meta.env.MODE !== 'test') {
      issues.push('Not running in test mode');
    }
    
    // Check for required test environment variables
    const requiredVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY'
    ];
    
    for (const varName of requiredVars) {
      if (!import.meta.env[varName]) {
        issues.push(`Missing environment variable: ${varName}`);
      }
    }
    
    return {
      isValid: issues.length === 0,
      issues
    };
  },

  /**
   * Setup test environment
   */
  setupTestEnvironment() {
    const mockEnv = MockFactory.createEnvironmentMock();
    
    // Apply mock environment variables
    Object.entries(mockEnv).forEach(([key, value]) => {
      Object.defineProperty(import.meta.env, key, {
        value,
        writable: true,
        configurable: true
      });
    });
    
    // Mock console methods to reduce noise
    global.console = {
      ...console,
      log: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn()
    };
    
    return mockEnv;
  },

  /**
   * Cleanup test environment
   */
  cleanupTestEnvironment() {
    vi.clearAllMocks();
    vi.resetAllMocks();
    vi.restoreAllMocks();
  }
};

// ============================================================================
// Test Assertions
// ============================================================================

export const TestAssertions = {
  /**
   * Assert that a payment response is valid
   */
  assertValidPaymentResponse(response: any) {
    expect(response).toBeDefined();
    expect(response.url).toBeDefined();
    expect(response.id).toBeDefined();
    expect(response.bookingId).toBeDefined();
    expect(typeof response.url).toBe('string');
    expect(response.url).toMatch(/^https?:\/\//);
  },

  /**
   * Assert that a booking record is valid
   */
  assertValidBookingRecord(booking: any) {
    expect(booking).toBeDefined();
    expect(booking.id).toBeDefined();
    expect(booking.service_type).toBeDefined();
    expect(booking.status).toBeDefined();
    expect(booking.contact).toBeDefined();
    expect(booking.contact.email).toMatch(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
  },

  /**
   * Assert that form data is properly sanitized
   */
  assertSanitizedFormData(formData: any) {
    expect(formData).toBeDefined();
    expect(formData.creditCard).toBeUndefined();
    expect(formData.cvv).toBeUndefined();
    expect(formData.ssn).toBeUndefined();
    expect(formData.password).toBeUndefined();
  },

  /**
   * Assert that error handling is working
   */
  assertErrorHandling(error: any, expectedType?: string) {
    expect(error).toBeDefined();
    expect(error.message).toBeDefined();
    expect(typeof error.message).toBe('string');
    
    if (expectedType) {
      expect(error.code || error.type).toBe(expectedType);
    }
  }
};
