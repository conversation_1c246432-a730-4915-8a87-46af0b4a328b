/* Responsive Design Utilities */
/* Enhanced device compatibility and responsive design patterns */

/* Base responsive utilities */
.responsive-container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .responsive-container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 768px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .responsive-container {
    max-width: 1536px;
  }
}

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.touch-target-large {
  min-height: 56px;
  min-width: 56px;
}

/* Mobile-first typography */
.responsive-text {
  font-size: 1rem;
  line-height: 1.5;
}

@media (min-width: 640px) {
  .responsive-text {
    font-size: 1.125rem;
    line-height: 1.6;
  }
}

@media (min-width: 768px) {
  .responsive-text {
    font-size: 1.25rem;
    line-height: 1.7;
  }
}

.responsive-heading {
  font-size: 1.5rem;
  line-height: 1.3;
  font-weight: 600;
}

@media (min-width: 640px) {
  .responsive-heading {
    font-size: 1.875rem;
    line-height: 1.4;
  }
}

@media (min-width: 768px) {
  .responsive-heading {
    font-size: 2.25rem;
    line-height: 1.4;
  }
}

@media (min-width: 1024px) {
  .responsive-heading {
    font-size: 3rem;
    line-height: 1.2;
  }
}

/* Responsive spacing utilities */
.responsive-spacing {
  padding: 1rem;
}

@media (min-width: 640px) {
  .responsive-spacing {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-spacing {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-spacing {
    padding: 3rem;
  }
}

/* Mobile-optimized forms */
.responsive-form {
  width: 100%;
}

.responsive-form input,
.responsive-form select,
.responsive-form textarea {
  width: 100%;
  min-height: 44px;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.responsive-form input:focus,
.responsive-form select:focus,
.responsive-form textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Prevent zoom on iOS when focusing inputs */
@media screen and (max-width: 768px) {
  .responsive-form input,
  .responsive-form select,
  .responsive-form textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Responsive grid layouts */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Mobile navigation optimizations */
.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 50;
  display: none;
}

.mobile-nav.active {
  display: flex;
}

.mobile-nav-content {
  background: white;
  width: 100%;
  max-width: 320px;
  height: 100%;
  padding: 2rem 1rem;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.mobile-nav.active .mobile-nav-content {
  transform: translateX(0);
}

/* Device-specific optimizations */

/* iOS specific fixes */
@supports (-webkit-touch-callout: none) {
  .ios-fix {
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Fix for iOS Safari viewport units */
  .ios-vh-fix {
    height: 100vh;
    height: -webkit-fill-available;
  }
}

/* Android specific fixes */
@media screen and (max-width: 768px) {
  .android-fix {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .respect-motion-preference {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .respect-motion-preference * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    background-color: #1f2937;
    color: #f9fafb;
  }
  
  .auto-dark input,
  .auto-dark select,
  .auto-dark textarea {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
}

/* Landscape orientation optimizations */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .landscape-compact {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  .landscape-compact .responsive-heading {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }
}

/* Print styles */
@media print {
  .print-hidden {
    display: none !important;
  }
  
  .print-visible {
    display: block !important;
  }
  
  .responsive-container {
    max-width: none;
    padding: 0;
  }
}

/* Accessibility enhancements */
.focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .high-contrast {
    border: 2px solid;
    background: white;
    color: black;
  }
}

/* Utility classes for common responsive patterns */
.hide-mobile {
  display: none;
}

@media (min-width: 768px) {
  .hide-mobile {
    display: block;
  }
}

.show-mobile {
  display: block;
}

@media (min-width: 768px) {
  .show-mobile {
    display: none;
  }
}

.stack-mobile {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .stack-mobile {
    flex-direction: row;
    align-items: center;
  }
}

/* Performance optimizations for mobile */
.mobile-optimized {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
}

/* Smooth scrolling with momentum on iOS */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Safe area insets for devices with notches */
.safe-area-insets {
  padding-top: env(safe-area-inset-top);
  padding-right: env(safe-area-inset-right);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
}

/* Flexible image containers */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 0.5rem;
}

.responsive-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
}

.responsive-image-container::before {
  content: '';
  display: block;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.responsive-image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}