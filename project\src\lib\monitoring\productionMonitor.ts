/**
 * Production Monitoring System
 * 
 * Comprehensive monitoring, logging, and alerting system for the booking platform.
 * Provides real-time insights into system health, performance, and business metrics.
 * 
 * CRITICAL: This addresses major gaps in production observability that impact
 * system reliability, debugging capabilities, and business intelligence.
 */

import { supabase } from '../supabase/client';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface MetricData {
  name: string;
  value: number;
  unit: string;
  timestamp: string;
  tags: Record<string, string>;
  metadata?: Record<string, any>;
}

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error' | 'critical';
  category: 'system' | 'business' | 'security' | 'performance' | 'user';
  message: string;
  context: Record<string, any>;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  source: string;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldownMinutes: number;
  lastTriggered?: string;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'critical';
  uptime: number;
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  pendingBookings: number;
  paymentSuccessRate: number;
  lastUpdated: string;
}

// ============================================================================
// Production Monitor Class
// ============================================================================

export class ProductionMonitor {
  private static instance: ProductionMonitor;
  private metrics: MetricData[] = [];
  private logs: LogEntry[] = [];
  private alertRules: AlertRule[] = [];
  private healthStatus: SystemHealth;
  private isInitialized = false;

  // Configuration
  private readonly MAX_METRICS_BUFFER = 1000;
  private readonly MAX_LOGS_BUFFER = 500;
  private readonly HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
  private readonly METRICS_FLUSH_INTERVAL = 60000; // 1 minute

  private constructor() {
    this.healthStatus = {
      status: 'healthy',
      uptime: 0,
      responseTime: 0,
      errorRate: 0,
      activeUsers: 0,
      pendingBookings: 0,
      paymentSuccessRate: 100,
      lastUpdated: new Date().toISOString()
    };
  }

  static getInstance(): ProductionMonitor {
    if (!ProductionMonitor.instance) {
      ProductionMonitor.instance = new ProductionMonitor();
    }
    return ProductionMonitor.instance;
  }

  /**
   * Initialize monitoring system
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load alert rules
      await this.loadAlertRules();

      // Start health monitoring
      this.startHealthMonitoring();

      // Start metrics collection
      this.startMetricsCollection();

      // Setup error tracking
      this.setupErrorTracking();

      // Setup performance monitoring
      this.setupPerformanceMonitoring();

      this.isInitialized = true;
      this.log('info', 'system', 'Production monitoring initialized successfully');

    } catch (error) {
      console.error('Failed to initialize production monitoring:', error);
      throw error;
    }
  }

  /**
   * Record a metric
   */
  recordMetric(
    name: string,
    value: number,
    unit: string = 'count',
    tags: Record<string, string> = {},
    metadata?: Record<string, any>
  ): void {
    const metric: MetricData = {
      name,
      value,
      unit,
      timestamp: new Date().toISOString(),
      tags,
      metadata
    };

    this.metrics.push(metric);

    // Maintain buffer size
    if (this.metrics.length > this.MAX_METRICS_BUFFER) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS_BUFFER);
    }

    // Check alert rules
    this.checkAlertRules(metric);
  }

  /**
   * Log an event
   */
  log(
    level: LogEntry['level'],
    category: LogEntry['category'],
    message: string,
    context: Record<string, any> = {},
    userId?: string,
    sessionId?: string,
    requestId?: string
  ): void {
    const logEntry: LogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      context: this.sanitizeContext(context),
      userId,
      sessionId,
      requestId,
      source: 'booking-system'
    };

    this.logs.push(logEntry);

    // Maintain buffer size
    if (this.logs.length > this.MAX_LOGS_BUFFER) {
      this.logs = this.logs.slice(-this.MAX_LOGS_BUFFER);
    }

    // Console output in development
    if (!import.meta.env.PROD) {
      this.consoleLog(logEntry);
    }

    // Send to external logging service in production
    if (import.meta.env.PROD) {
      this.sendToExternalLogging(logEntry);
    }
  }

  /**
   * Track booking events
   */
  trackBookingEvent(
    event: 'started' | 'form_completed' | 'payment_initiated' | 'payment_completed' | 'booking_confirmed' | 'booking_failed',
    bookingId?: string,
    userId?: string,
    metadata?: Record<string, any>
  ): void {
    this.recordMetric(
      `booking.${event}`,
      1,
      'count',
      {
        event,
        booking_id: bookingId || 'unknown',
        user_id: userId || 'anonymous'
      },
      metadata
    );

    this.log(
      'info',
      'business',
      `Booking event: ${event}`,
      {
        event,
        bookingId,
        userId,
        ...metadata
      },
      userId
    );
  }

  /**
   * Track payment events
   */
  trackPaymentEvent(
    event: 'initiated' | 'processing' | 'completed' | 'failed' | 'refunded',
    paymentId: string,
    amount?: number,
    currency: string = 'USD',
    metadata?: Record<string, any>
  ): void {
    this.recordMetric(
      `payment.${event}`,
      1,
      'count',
      {
        event,
        payment_id: paymentId,
        currency
      },
      { amount, ...metadata }
    );

    if (amount) {
      this.recordMetric(
        `payment.amount.${event}`,
        amount,
        currency,
        {
          event,
          payment_id: paymentId,
          currency
        }
      );
    }

    this.log(
      event === 'failed' ? 'error' : 'info',
      'business',
      `Payment event: ${event}`,
      {
        event,
        paymentId,
        amount,
        currency,
        ...metadata
      }
    );
  }

  /**
   * Track performance metrics
   */
  trackPerformance(
    operation: string,
    duration: number,
    success: boolean,
    metadata?: Record<string, any>
  ): void {
    this.recordMetric(
      `performance.${operation}.duration`,
      duration,
      'ms',
      {
        operation,
        success: success.toString()
      },
      metadata
    );

    this.recordMetric(
      `performance.${operation}.${success ? 'success' : 'failure'}`,
      1,
      'count',
      { operation }
    );

    if (!success) {
      this.log(
        'warn',
        'performance',
        `Performance issue in ${operation}`,
        {
          operation,
          duration,
          ...metadata
        }
      );
    }
  }

  /**
   * Track user activity
   */
  trackUserActivity(
    action: string,
    userId?: string,
    sessionId?: string,
    metadata?: Record<string, any>
  ): void {
    this.recordMetric(
      `user.activity.${action}`,
      1,
      'count',
      {
        action,
        user_id: userId || 'anonymous',
        session_id: sessionId || 'unknown'
      },
      metadata
    );

    this.log(
      'info',
      'user',
      `User activity: ${action}`,
      {
        action,
        userId,
        sessionId,
        ...metadata
      },
      userId,
      sessionId
    );
  }

  /**
   * Get current system health
   */
  getSystemHealth(): SystemHealth {
    return { ...this.healthStatus };
  }

  /**
   * Get recent metrics
   */
  getMetrics(
    name?: string,
    since?: string,
    limit: number = 100
  ): MetricData[] {
    let filteredMetrics = this.metrics;

    if (name) {
      filteredMetrics = filteredMetrics.filter(m => m.name === name);
    }

    if (since) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp >= since);
    }

    return filteredMetrics.slice(-limit);
  }

  /**
   * Get recent logs
   */
  getLogs(
    level?: LogEntry['level'],
    category?: LogEntry['category'],
    since?: string,
    limit: number = 100
  ): LogEntry[] {
    let filteredLogs = this.logs;

    if (level) {
      filteredLogs = filteredLogs.filter(l => l.level === level);
    }

    if (category) {
      filteredLogs = filteredLogs.filter(l => l.category === category);
    }

    if (since) {
      filteredLogs = filteredLogs.filter(l => l.timestamp >= since);
    }

    return filteredLogs.slice(-limit);
  }

  /**
   * Generate dashboard data
   */
  getDashboardData(): {
    health: SystemHealth;
    recentMetrics: Record<string, MetricData[]>;
    recentLogs: LogEntry[];
    alerts: AlertRule[];
  } {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000).toISOString();

    // Group recent metrics by name
    const recentMetrics: Record<string, MetricData[]> = {};
    this.getMetrics(undefined, oneHourAgo).forEach(metric => {
      if (!recentMetrics[metric.name]) {
        recentMetrics[metric.name] = [];
      }
      recentMetrics[metric.name].push(metric);
    });

    return {
      health: this.getSystemHealth(),
      recentMetrics,
      recentLogs: this.getLogs(undefined, undefined, oneHourAgo, 50),
      alerts: this.alertRules.filter(rule => rule.enabled)
    };
  }

  // Private methods...
  private async loadAlertRules(): Promise<void> {
    // Default alert rules
    this.alertRules = [
      {
        id: 'high-error-rate',
        name: 'High Error Rate',
        condition: 'error_rate > threshold',
        threshold: 5, // 5%
        severity: 'high',
        enabled: true,
        cooldownMinutes: 15
      },
      {
        id: 'slow-response-time',
        name: 'Slow Response Time',
        condition: 'avg_response_time > threshold',
        threshold: 2000, // 2 seconds
        severity: 'medium',
        enabled: true,
        cooldownMinutes: 10
      },
      {
        id: 'payment-failure-spike',
        name: 'Payment Failure Spike',
        condition: 'payment_failure_rate > threshold',
        threshold: 10, // 10%
        severity: 'critical',
        enabled: true,
        cooldownMinutes: 5
      }
    ];
  }

  private startHealthMonitoring(): void {
    setInterval(async () => {
      await this.updateSystemHealth();
    }, this.HEALTH_CHECK_INTERVAL);
  }

  private startMetricsCollection(): void {
    setInterval(() => {
      this.flushMetrics();
    }, this.METRICS_FLUSH_INTERVAL);
  }

  private async updateSystemHealth(): Promise<void> {
    try {
      const startTime = Date.now();
      
      // Test database connectivity
      const { error: dbError } = await supabase
        .from('booking_forms')
        .select('count')
        .limit(1);

      const responseTime = Date.now() - startTime;

      // Calculate error rate from recent logs
      const recentErrors = this.getLogs('error', undefined, 
        new Date(Date.now() - 5 * 60 * 1000).toISOString()
      );
      const totalRecentLogs = this.getLogs(undefined, undefined,
        new Date(Date.now() - 5 * 60 * 1000).toISOString()
      );
      
      const errorRate = totalRecentLogs.length > 0 
        ? (recentErrors.length / totalRecentLogs.length) * 100 
        : 0;

      // Update health status
      this.healthStatus = {
        status: this.determineHealthStatus(dbError, responseTime, errorRate),
        uptime: Date.now() - (performance.timeOrigin || 0),
        responseTime,
        errorRate,
        activeUsers: this.getActiveUsersCount(),
        pendingBookings: await this.getPendingBookingsCount(),
        paymentSuccessRate: this.getPaymentSuccessRate(),
        lastUpdated: new Date().toISOString()
      };

    } catch (error) {
      this.healthStatus.status = 'critical';
      this.log('error', 'system', 'Health check failed', { error: error.message });
    }
  }

  private determineHealthStatus(
    dbError: any,
    responseTime: number,
    errorRate: number
  ): SystemHealth['status'] {
    if (dbError || responseTime > 5000 || errorRate > 10) {
      return 'critical';
    }
    if (responseTime > 2000 || errorRate > 5) {
      return 'degraded';
    }
    return 'healthy';
  }

  private getActiveUsersCount(): number {
    // Count unique users from recent activity
    const recentActivity = this.getLogs('info', 'user', 
      new Date(Date.now() - 15 * 60 * 1000).toISOString()
    );
    
    const uniqueUsers = new Set(
      recentActivity
        .filter(log => log.userId)
        .map(log => log.userId)
    );
    
    return uniqueUsers.size;
  }

  private async getPendingBookingsCount(): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('booking_forms')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending');

      return error ? 0 : (count || 0);
    } catch {
      return 0;
    }
  }

  private getPaymentSuccessRate(): number {
    const paymentMetrics = this.getMetrics('payment.completed', 
      new Date(Date.now() - 60 * 60 * 1000).toISOString()
    );
    const paymentFailures = this.getMetrics('payment.failed',
      new Date(Date.now() - 60 * 60 * 1000).toISOString()
    );

    const total = paymentMetrics.length + paymentFailures.length;
    return total > 0 ? (paymentMetrics.length / total) * 100 : 100;
  }

  private checkAlertRules(metric: MetricData): void {
    // Implementation for alert rule checking
    // This would trigger alerts based on metric thresholds
  }

  private flushMetrics(): void {
    // In production, send metrics to external monitoring service
    if (import.meta.env.PROD && this.metrics.length > 0) {
      this.sendMetricsToExternalService(this.metrics.slice());
      this.metrics = [];
    }
  }

  private setupErrorTracking(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.log('error', 'system', 'Unhandled error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });

    // Promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.log('error', 'system', 'Unhandled promise rejection', {
        reason: event.reason?.toString()
      });
    });
  }

  private setupPerformanceMonitoring(): void {
    // Monitor navigation timing
    if (typeof window !== 'undefined' && window.performance) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          if (navigation) {
            this.recordMetric('performance.page_load', navigation.loadEventEnd - navigation.fetchStart, 'ms');
            this.recordMetric('performance.dom_ready', navigation.domContentLoadedEventEnd - navigation.fetchStart, 'ms');
          }
        }, 0);
      });
    }
  }

  private consoleLog(entry: LogEntry): void {
    const colors = {
      debug: '\x1b[36m',
      info: '\x1b[32m',
      warn: '\x1b[33m',
      error: '\x1b[31m',
      critical: '\x1b[35m'
    };
    
    console.log(
      `${colors[entry.level]}[${entry.timestamp}] ${entry.level.toUpperCase()} [${entry.category}]: ${entry.message}\x1b[0m`,
      entry.context
    );
  }

  private sendToExternalLogging(entry: LogEntry): void {
    // Implementation for external logging service
    // This would send logs to services like DataDog, New Relic, etc.
  }

  private sendMetricsToExternalService(metrics: MetricData[]): void {
    // Implementation for external metrics service
    // This would send metrics to services like DataDog, New Relic, etc.
  }

  private sanitizeContext(context: Record<string, any>): Record<string, any> {
    const sanitized = { ...context };
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'creditCard', 'cvv', 'ssn', 'token', 'apiKey'];
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// ============================================================================
// Monitoring Hooks and Utilities
// ============================================================================

/**
 * React hook for monitoring integration
 */
export function useProductionMonitor() {
  const monitor = ProductionMonitor.getInstance();

  const trackEvent = (
    category: 'booking' | 'payment' | 'user' | 'performance',
    action: string,
    metadata?: Record<string, any>
  ) => {
    switch (category) {
      case 'booking':
        monitor.trackBookingEvent(action as any, metadata?.bookingId, metadata?.userId, metadata);
        break;
      case 'payment':
        monitor.trackPaymentEvent(action as any, metadata?.paymentId, metadata?.amount, metadata?.currency, metadata);
        break;
      case 'user':
        monitor.trackUserActivity(action, metadata?.userId, metadata?.sessionId, metadata);
        break;
      case 'performance':
        monitor.trackPerformance(action, metadata?.duration || 0, metadata?.success || true, metadata);
        break;
    }
  };

  const logEvent = (
    level: LogEntry['level'],
    category: LogEntry['category'],
    message: string,
    context?: Record<string, any>
  ) => {
    monitor.log(level, category, message, context);
  };

  return {
    trackEvent,
    logEvent,
    getHealth: () => monitor.getSystemHealth(),
    getDashboard: () => monitor.getDashboardData()
  };
}

/**
 * Performance monitoring decorator
 */
export function monitorPerformance(operationName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const monitor = ProductionMonitor.getInstance();
      const startTime = Date.now();
      let success = true;
      let error: any = null;

      try {
        const result = await method.apply(this, args);
        return result;
      } catch (err) {
        success = false;
        error = err;
        throw err;
      } finally {
        const duration = Date.now() - startTime;
        monitor.trackPerformance(operationName, duration, success, {
          method: propertyName,
          args: args.length,
          error: error?.message
        });
      }
    };

    return descriptor;
  };
}

/**
 * Initialize monitoring for the application
 */
export async function initializeMonitoring(): Promise<void> {
  const monitor = ProductionMonitor.getInstance();
  await monitor.initialize();

  // Track application startup
  monitor.log('info', 'system', 'Application started', {
    environment: import.meta.env.MODE,
    timestamp: new Date().toISOString()
  });
}
