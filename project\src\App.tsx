import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Header } from './components/layout/Header';
import { AnimatedBackground } from './components/layout/AnimatedBackground';
import { Hero } from './components/home/<USER>';
import { ServiceGrid } from './components/services/ServiceGrid';
import { PhotoGallery } from './components/home/<USER>';
import { IndustriesSection } from './components/industries/IndustriesSection';
import { CleaningSimplified } from './components/home/<USER>';
import { TestimonialsSection } from './components/testimonials/TestimonialsSection';
import { CompanyOverview } from './components/home/<USER>';
import { CtaSection } from './components/home/<USER>';
import { Footer } from './components/layout/Footer';
import MobileBottomNav from './components/mobile/MobileBottomNav';
import { preventScroll, restoreScroll } from './utils/scrollOptimization';
import { initializeDeviceCompatibility } from './utils/deviceCompatibility';
import { initializePWA } from './utils/serviceWorker';

// Consistent Section wrapper component
const Section: React.FC<{ children: React.ReactNode, className?: string }> = ({ children, className = '' }) => (
  <section className={`py-8 sm:py-12 ${className}`}>
    {children}
  </section>
);

export default function App() {
  const navigate = useNavigate();
  const [showServiceMenu, setShowServiceMenu] = useState(false);

  const handleServiceSelect = (serviceId: string) => {
    navigate(`/service-form/${serviceId}`);
    setShowServiceMenu(false);
  };

  const handleBookClick = () => {
    setShowServiceMenu(true);
  };

  const handleCloseModal = () => {
    setShowServiceMenu(false);
  };

  // Initialize device compatibility features
  useEffect(() => {
    // Initialize device compatibility and PWA features
    initializeDeviceCompatibility();
    initializePWA();
  }, []);

  // Handle scroll prevention when modal is open
  useEffect(() => {
    if (showServiceMenu) {
      preventScroll();
    } else {
      restoreScroll();
    }

    // Cleanup on unmount
    return () => {
      restoreScroll();
    };
  }, [showServiceMenu]);

  return (
    <AnimatedBackground>
      <div className="relative">
        {/* Floating Design Elements */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
          <motion.div
            animate={{
              y: [0, -22, 0],
              rotate: [0, 6, 0],
              scale: [1, 1.05, 1]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-1/6 right-1/7 w-36 h-36 border border-white/5 rounded-lg"
          />
          <motion.div
            animate={{
              y: [0, 18, 0],
              rotate: [0, -4, 0],
              scale: [1, 0.95, 1]
            }}
            transition={{
              duration: 13,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2.5
            }}
            className="absolute bottom-1/4 left-1/12 w-28 h-28 border border-emerald-400/10 rounded-full"
          />
          <motion.div
            animate={{
              x: [0, 12, 0],
              y: [0, -8, 0],
              rotate: [0, 2, 0]
            }}
            transition={{
              duration: 16,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 5
            }}
            className="absolute top-2/3 right-1/4 w-20 h-20 bg-white/5 rounded-xl"
          />
          <motion.div
            animate={{
              y: [0, -15, 0],
              x: [0, 6, 0],
              scale: [1, 1.12, 1]
            }}
            transition={{
              duration: 11,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 7.5
            }}
            className="absolute top-2/5 left-1/6 w-18 h-24 border border-blue-400/10 rounded-lg"
          />
        </div>

        <Header />

        <div className="relative z-10 pt-20 sm:pt-24 lg:pt-28">
          <main>
            {/* Hero Section */}
            <Section className="!pt-0">
              <Hero onBook={handleBookClick} />
            </Section>

            {/* Section Divider */}
            <div className="relative py-8">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full max-w-md h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              </div>
              <div className="relative flex justify-center space-x-3">
                <div className="w-2 h-2 bg-emerald-400 rounded-full opacity-40"></div>
                <div className="w-3 h-3 bg-emerald-400 rounded-full opacity-60"></div>
                <div className="w-2 h-2 bg-emerald-400 rounded-full opacity-40"></div>
              </div>
            </div>

            {/* Services Section */}
            <Section>
              <ServiceGrid onServiceSelect={handleServiceSelect} />
            </Section>

            {/* Section Divider */}
            <div className="relative py-8">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full max-w-lg h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              </div>
              <div className="relative flex justify-center">
                <div className="w-4 h-4 bg-white/10 rotate-45"></div>
              </div>
            </div>

            {/* Photo Gallery Section */}
            <Section>
              <PhotoGallery />
            </Section>

            {/* Section Divider */}
            <div className="relative py-10">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full max-w-xs h-px bg-gradient-to-r from-transparent via-emerald-400/30 to-transparent"></div>
              </div>
              <div className="relative flex justify-center space-x-6">
                <div className="w-1 h-1 bg-emerald-400 rounded-full opacity-30"></div>
                <div className="w-2 h-2 bg-emerald-400 rounded-full opacity-50"></div>
                <div className="w-3 h-3 bg-emerald-400 rounded-full opacity-70"></div>
                <div className="w-2 h-2 bg-emerald-400 rounded-full opacity-50"></div>
                <div className="w-1 h-1 bg-emerald-400 rounded-full opacity-30"></div>
              </div>
            </div>

            {/* Industries Section */}
            <Section>
              <IndustriesSection />
            </Section>

            {/* Section Divider */}
            <div className="relative py-8">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full max-w-sm h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              </div>
              <div className="relative flex justify-center">
                <div className="w-3 h-3 bg-blue-400 rounded-full opacity-50"></div>
              </div>
            </div>

            {/* Company Overview Section */}
            <Section>
              <CompanyOverview />
            </Section>

            {/* Section Divider */}
            <div className="relative py-8">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full max-w-lg h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              </div>
              <div className="relative flex justify-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 border border-white/20 rounded-full"
                />
              </div>
            </div>

            {/* Cleaning Simplified Section */}
            <Section>
              <CleaningSimplified onBook={handleBookClick} />
            </Section>

            {/* Section Divider */}
            <div className="relative py-8">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full max-w-xs h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              </div>
              <div className="relative flex justify-center">
                <div className="w-3 h-3 bg-purple-400 rounded-full opacity-50"></div>
              </div>
            </div>

            {/* Testimonials Section */}
            <Section>
              <TestimonialsSection />
            </Section>

            {/* Section Divider */}
            <div className="relative py-8">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-full max-w-lg h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              </div>
              <div className="relative flex justify-center">
                <motion.div
                  animate={{ 
                    scale: [1, 1.2, 1],
                    opacity: [0.3, 0.7, 0.3]
                  }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                  className="w-3 h-3 bg-emerald-400 rounded-full"
                />
              </div>
            </div>

            {/* Call to Action Section */}
            <Section>
              <CtaSection />
            </Section>
          </main>
          
          <Footer />
        </div>

        <MobileBottomNav />

        {/* Enhanced Service Selection Modal */
        {showServiceMenu && (
          <div 
            className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
            onClick={handleCloseModal}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2 }}
              className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-900">Select Service Type</h3>
                <p className="text-sm text-gray-600 mt-1">Choose the cleaning service you need</p>
              </div>
              
              {/* Scrollable Modal Content */}
              <div className="overflow-y-auto max-h-[50vh] p-6" data-scrollable>
                <div className="space-y-3">
                  <button
                    onClick={() => handleServiceSelect('office')}
                    className="w-full text-left px-4 py-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 border border-gray-200 hover:border-gray-300"
                  >
                    <div className="font-medium text-gray-900">Office Cleaning</div>
                    <div className="text-sm text-gray-600">Professional office and workspace cleaning</div>
                  </button>
                  
                  <button
                    onClick={() => handleServiceSelect('commercial')}
                    className="w-full text-left px-4 py-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 border border-gray-200 hover:border-gray-300"
                  >
                    <div className="font-medium text-gray-900">Commercial Cleaning</div>
                    <div className="text-sm text-gray-600">Large-scale commercial facility cleaning</div>
                  </button>
                  
                  <button
                    onClick={() => handleServiceSelect('industrial')}
                    className="w-full text-left px-4 py-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 border border-gray-200 hover:border-gray-300"
                  >
                    <div className="font-medium text-gray-900">Industrial Cleaning</div>
                    <div className="text-sm text-gray-600">Specialized industrial facility cleaning</div>
                  </button>
                  
                  <button
                    onClick={() => handleServiceSelect('residential')}
                    className="w-full text-left px-4 py-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 border border-gray-200 hover:border-gray-300"
                  >
                    <div className="font-medium text-gray-900">Residential Cleaning</div>
                    <div className="text-sm text-gray-600">Home and apartment cleaning services</div>
                  </button>
                </div>
              </div>
              
              {/* Modal Footer */}
              <div className="px-6 py-4 border-t border-gray-200">
                <button
                  onClick={handleCloseModal}
                  className="w-full px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors duration-200 font-medium text-gray-700"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </AnimatedBackground>
  );
}