// Responsive Testing and Device Compatibility Utilities

import { defaultMobileConfig, featureSupport } from '../config/mobileConfig';

interface DeviceTest {
  name: string;
  description: string;
  test: () => boolean | Promise<boolean>;
  severity: 'error' | 'warning' | 'info';
  recommendation?: string;
}

interface TestResult {
  name: string;
  passed: boolean;
  severity: 'error' | 'warning' | 'info';
  message: string;
  recommendation?: string;
}

interface ResponsiveTestSuite {
  viewport: DeviceTest[];
  touch: DeviceTest[];
  performance: DeviceTest[];
  accessibility: DeviceTest[];
  pwa: DeviceTest[];
  network: DeviceTest[];
}

// Viewport and Layout Tests
const viewportTests: DeviceTest[] = [
  {
    name: 'Viewport Meta Tag',
    description: 'Check if viewport meta tag is properly configured',
    test: () => {
      const viewport = document.querySelector('meta[name="viewport"]');
      return viewport !== null && viewport.getAttribute('content')?.includes('width=device-width') === true;
    },
    severity: 'error',
    recommendation: 'Add <meta name="viewport" content="width=device-width, initial-scale=1.0"> to your HTML head'
  },
  {
    name: 'Responsive Breakpoints',
    description: 'Test if CSS breakpoints are working correctly',
    test: () => {
      const testElement = document.createElement('div');
      testElement.style.cssText = 'width: 100%; height: 1px; position: absolute; top: -9999px;';
      document.body.appendChild(testElement);
      
      const width = testElement.offsetWidth;
      document.body.removeChild(testElement);
      
      return width > 0;
    },
    severity: 'warning',
    recommendation: 'Ensure CSS breakpoints are properly defined and working'
  },
  {
    name: 'Horizontal Scroll',
    description: 'Check for unwanted horizontal scrolling',
    test: () => {
      return document.documentElement.scrollWidth <= window.innerWidth;
    },
    severity: 'warning',
    recommendation: 'Fix elements causing horizontal overflow'
  },
  {
    name: 'Safe Area Support',
    description: 'Check if safe area insets are supported',
    test: () => {
      const testElement = document.createElement('div');
      testElement.style.paddingTop = 'env(safe-area-inset-top)';
      document.body.appendChild(testElement);
      
      const computedStyle = window.getComputedStyle(testElement);
      const hasSafeArea = computedStyle.paddingTop !== '0px';
      
      document.body.removeChild(testElement);
      return hasSafeArea;
    },
    severity: 'info',
    recommendation: 'Consider adding safe area support for better mobile experience'
  }
];

// Touch and Interaction Tests
const touchTests: DeviceTest[] = [
  {
    name: 'Touch Support',
    description: 'Check if touch events are supported',
    test: () => featureSupport.hasTouch(),
    severity: 'info',
    recommendation: 'Ensure touch interactions are properly implemented'
  },
  {
    name: 'Touch Target Size',
    description: 'Check if interactive elements meet minimum touch target size',
    test: () => {
      const buttons = document.querySelectorAll('button, a, input[type="button"], input[type="submit"]');
      const minSize = defaultMobileConfig.touch.minTouchTarget;
      
      for (const button of buttons) {
        const rect = button.getBoundingClientRect();
        if (rect.width < minSize || rect.height < minSize) {
          return false;
        }
      }
      return true;
    },
    severity: 'warning',
    recommendation: `Ensure interactive elements are at least ${defaultMobileConfig.touch.minTouchTarget}px in both dimensions`
  },
  {
    name: 'Haptic Feedback',
    description: 'Check if haptic feedback is available',
    test: () => featureSupport.hasHaptics(),
    severity: 'info',
    recommendation: 'Consider adding haptic feedback for better mobile UX'
  }
];

// Performance Tests
const performanceTests: DeviceTest[] = [
  {
    name: 'Page Load Time',
    description: 'Check if page loads within acceptable time',
    test: () => {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      return loadTime < 3000; // 3 seconds
    },
    severity: 'warning',
    recommendation: 'Optimize page load time to under 3 seconds'
  },
  {
    name: 'First Contentful Paint',
    description: 'Check First Contentful Paint timing',
    test: async () => {
      return new Promise((resolve) => {
        if ('PerformanceObserver' in window) {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const fcp = entries.find(entry => entry.name === 'first-contentful-paint');
            resolve(fcp ? fcp.startTime < 2000 : true); // 2 seconds
          });
          observer.observe({ entryTypes: ['paint'] });
          
          // Fallback timeout
          setTimeout(() => resolve(true), 1000);
        } else {
          resolve(true);
        }
      });
    },
    severity: 'warning',
    recommendation: 'Optimize First Contentful Paint to under 2 seconds'
  },
  {
    name: 'Memory Usage',
    description: 'Check if memory usage is within reasonable limits',
    test: () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        return usedMB < 50; // 50MB
      }
      return true;
    },
    severity: 'info',
    recommendation: 'Monitor and optimize memory usage'
  }
];

// Accessibility Tests
const accessibilityTests: DeviceTest[] = [
  {
    name: 'Color Contrast',
    description: 'Check if text has sufficient color contrast',
    test: () => {
      // Simplified contrast check - in real implementation, you'd use a proper contrast calculation
      const elements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div');
      // This is a placeholder - implement actual contrast checking
      return elements.length > 0;
    },
    severity: 'warning',
    recommendation: 'Ensure text has sufficient color contrast (WCAG AA: 4.5:1)'
  },
  {
    name: 'Alt Text for Images',
    description: 'Check if images have alt text',
    test: () => {
      const images = document.querySelectorAll('img');
      for (const img of images) {
        if (!img.getAttribute('alt')) {
          return false;
        }
      }
      return true;
    },
    severity: 'warning',
    recommendation: 'Add descriptive alt text to all images'
  },
  {
    name: 'Keyboard Navigation',
    description: 'Check if interactive elements are keyboard accessible',
    test: () => {
      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
      for (const element of interactiveElements) {
        const tabIndex = element.getAttribute('tabindex');
        if (tabIndex === '-1') {
          return false;
        }
      }
      return true;
    },
    severity: 'error',
    recommendation: 'Ensure all interactive elements are keyboard accessible'
  }
];

// PWA Tests
const pwaTests: DeviceTest[] = [
  {
    name: 'Service Worker',
    description: 'Check if service worker is registered',
    test: async () => {
      if (!featureSupport.hasServiceWorker()) {
        return false;
      }
      
      try {
        const registration = await navigator.serviceWorker.getRegistration();
        return registration !== undefined;
      } catch {
        return false;
      }
    },
    severity: 'info',
    recommendation: 'Register a service worker for offline functionality'
  },
  {
    name: 'Web App Manifest',
    description: 'Check if web app manifest is present',
    test: () => {
      const manifest = document.querySelector('link[rel="manifest"]');
      return manifest !== null;
    },
    severity: 'info',
    recommendation: 'Add a web app manifest for PWA functionality'
  },
  {
    name: 'HTTPS',
    description: 'Check if site is served over HTTPS',
    test: () => {
      return location.protocol === 'https:' || location.hostname === 'localhost';
    },
    severity: 'warning',
    recommendation: 'Serve your app over HTTPS for PWA features'
  }
];

// Network Tests
const networkTests: DeviceTest[] = [
  {
    name: 'Online Status',
    description: 'Check if browser can detect online/offline status',
    test: () => {
      return 'onLine' in navigator;
    },
    severity: 'info',
    recommendation: 'Implement offline handling for better UX'
  },
  {
    name: 'Connection Type',
    description: 'Check if connection type can be detected',
    test: () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      return connection !== undefined;
    },
    severity: 'info',
    recommendation: 'Adapt content based on connection type'
  }
];

// Complete test suite
const responsiveTestSuite: ResponsiveTestSuite = {
  viewport: viewportTests,
  touch: touchTests,
  performance: performanceTests,
  accessibility: accessibilityTests,
  pwa: pwaTests,
  network: networkTests
};

// Test runner
export const runResponsiveTests = async (categories?: (keyof ResponsiveTestSuite)[]): Promise<TestResult[]> => {
  const results: TestResult[] = [];
  const categoriesToTest = categories || Object.keys(responsiveTestSuite) as (keyof ResponsiveTestSuite)[];
  
  for (const category of categoriesToTest) {
    const tests = responsiveTestSuite[category];
    
    for (const test of tests) {
      try {
        const passed = await test.test();
        results.push({
          name: `${category}: ${test.name}`,
          passed,
          severity: test.severity,
          message: test.description,
          recommendation: test.recommendation
        });
      } catch (error) {
        results.push({
          name: `${category}: ${test.name}`,
          passed: false,
          severity: 'error',
          message: `Test failed: ${error}`,
          recommendation: test.recommendation
        });
      }
    }
  }
  
  return results;
};

// Generate test report
export const generateTestReport = (results: TestResult[]): string => {
  const passed = results.filter(r => r.passed).length;
  const failed = results.filter(r => !r.passed).length;
  const errors = results.filter(r => !r.passed && r.severity === 'error').length;
  const warnings = results.filter(r => !r.passed && r.severity === 'warning').length;
  
  let report = `\n=== Responsive Design Test Report ===\n`;
  report += `Total Tests: ${results.length}\n`;
  report += `Passed: ${passed}\n`;
  report += `Failed: ${failed}\n`;
  report += `Errors: ${errors}\n`;
  report += `Warnings: ${warnings}\n\n`;
  
  if (failed > 0) {
    report += `Failed Tests:\n`;
    results.filter(r => !r.passed).forEach(result => {
      report += `❌ ${result.name} (${result.severity.toUpperCase()})\n`;
      report += `   ${result.message}\n`;
      if (result.recommendation) {
        report += `   💡 ${result.recommendation}\n`;
      }
      report += `\n`;
    });
  }
  
  return report;
};

// Quick device compatibility check
export const quickCompatibilityCheck = async (): Promise<{ score: number; issues: string[] }> => {
  const results = await runResponsiveTests(['viewport', 'touch', 'accessibility']);
  const totalTests = results.length;
  const passedTests = results.filter(r => r.passed).length;
  const score = Math.round((passedTests / totalTests) * 100);
  
  const issues = results
    .filter(r => !r.passed && r.severity === 'error')
    .map(r => r.recommendation || r.message);
  
  return { score, issues };
};

// Device simulation utilities
export const simulateDevice = (deviceType: 'mobile' | 'tablet' | 'desktop') => {
  const viewports = {
    mobile: { width: 375, height: 667 },
    tablet: { width: 768, height: 1024 },
    desktop: { width: 1920, height: 1080 }
  };
  
  const viewport = viewports[deviceType];
  
  // This would typically be used in testing environments
  console.log(`Simulating ${deviceType} device: ${viewport.width}x${viewport.height}`);
  
  return viewport;
};

export default {
  runResponsiveTests,
  generateTestReport,
  quickCompatibilityCheck,
  simulateDevice,
  responsiveTestSuite
};