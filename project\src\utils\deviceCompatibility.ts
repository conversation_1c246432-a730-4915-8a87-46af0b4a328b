/**
 * Device Compatibility Utilities
 * Comprehensive utilities for cross-device compatibility and responsive design
 */

// Device detection types
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isWindows: boolean;
  isMac: boolean;
  screenSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
}

// Responsive breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const;

// Touch target minimum sizes (following accessibility guidelines)
export const TOUCH_TARGETS = {
  minimum: 44, // 44px minimum for accessibility
  comfortable: 48, // 48px for comfortable touch
  large: 56 // 56px for primary actions
} as const;

/**
 * Detect device type and capabilities
 */
export function getDeviceInfo(): DeviceInfo {
  const userAgent = navigator.userAgent.toLowerCase();
  const width = window.innerWidth;
  const height = window.innerHeight;
  
  // Device type detection
  const isMobile = width < BREAKPOINTS.md || /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
  const isTablet = (width >= BREAKPOINTS.md && width < BREAKPOINTS.lg) || /ipad|tablet/i.test(userAgent);
  const isDesktop = width >= BREAKPOINTS.lg && !isMobile && !isTablet;
  
  // Touch capability
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  // Operating system detection
  const isIOS = /iphone|ipad|ipod/i.test(userAgent);
  const isAndroid = /android/i.test(userAgent);
  const isWindows = /windows/i.test(userAgent);
  const isMac = /macintosh|mac os x/i.test(userAgent);
  
  // Screen size category
  let screenSize: DeviceInfo['screenSize'] = 'xs';
  if (width >= BREAKPOINTS['2xl']) screenSize = '2xl';
  else if (width >= BREAKPOINTS.xl) screenSize = 'xl';
  else if (width >= BREAKPOINTS.lg) screenSize = 'lg';
  else if (width >= BREAKPOINTS.md) screenSize = 'md';
  else if (width >= BREAKPOINTS.sm) screenSize = 'sm';
  
  // Orientation
  const orientation = height > width ? 'portrait' : 'landscape';
  
  // Pixel ratio
  const pixelRatio = window.devicePixelRatio || 1;
  
  return {
    isMobile,
    isTablet,
    isDesktop,
    isTouchDevice,
    isIOS,
    isAndroid,
    isWindows,
    isMac,
    screenSize,
    orientation,
    pixelRatio
  };
}

/**
 * Hook for reactive device info
 */
export function useDeviceInfo() {
  const [deviceInfo, setDeviceInfo] = React.useState<DeviceInfo>(getDeviceInfo);
  
  React.useEffect(() => {
    const handleResize = () => {
      setDeviceInfo(getDeviceInfo());
    };
    
    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(() => {
        setDeviceInfo(getDeviceInfo());
      }, 100);
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);
  
  return deviceInfo;
}

/**
 * Optimize touch interactions for mobile devices
 */
export function optimizeTouchInteractions(): void {
  // Prevent 300ms click delay on mobile
  const style = document.createElement('style');
  style.textContent = `
    * {
      touch-action: manipulation;
    }
    
    button, [role="button"], input[type="submit"], input[type="button"] {
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;
    }
    
    /* Ensure minimum touch target sizes */
    button, [role="button"], input, select, textarea {
      min-height: ${TOUCH_TARGETS.minimum}px;
      min-width: ${TOUCH_TARGETS.minimum}px;
    }
    
    /* Optimize scrolling on iOS */
    .scroll-container {
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: contain;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Apply mobile-specific optimizations
 */
export function applyMobileOptimizations(): void {
  const deviceInfo = getDeviceInfo();
  
  if (deviceInfo.isMobile) {
    // Prevent zoom on input focus (iOS)
    const viewportMeta = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;
    if (viewportMeta) {
      viewportMeta.content = 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no';
    }
    
    // Optimize font rendering
    document.body.style.setProperty('-webkit-font-smoothing', 'antialiased');
    document.body.style.setProperty('-moz-osx-font-smoothing', 'grayscale');
    
    // Optimize scrolling
    document.body.style.setProperty('-webkit-overflow-scrolling', 'touch');
    document.body.style.setProperty('overscroll-behavior', 'contain');
    
    // Prevent text size adjustment on orientation change
    document.body.style.setProperty('-webkit-text-size-adjust', '100%');
    document.body.style.setProperty('-ms-text-size-adjust', '100%');
  }
}

/**
 * Cross-browser compatibility fixes
 */
export function applyCrossBrowserFixes(): void {
  // CSS custom properties fallback for older browsers
  if (!CSS.supports('color', 'var(--primary)')) {
    const style = document.createElement('style');
    style.textContent = `
      :root {
        --primary: #3b82f6;
        --secondary: #64748b;
        --accent: #f59e0b;
        --background: #ffffff;
        --foreground: #0f172a;
      }
    `;
    document.head.appendChild(style);
  }
  
  // Flexbox gap fallback
  if (!CSS.supports('gap', '1rem')) {
    const style = document.createElement('style');
    style.textContent = `
      .flex-gap > * + * {
        margin-left: 1rem;
      }
      
      .grid-gap > * {
        margin-bottom: 1rem;
      }
    `;
    document.head.appendChild(style);
  }
  
  // Smooth scrolling fallback
  if (!('scrollBehavior' in document.documentElement.style)) {
    // Polyfill for smooth scrolling
    const smoothScrollPolyfill = () => {
      const links = document.querySelectorAll('a[href^="#"]');
      links.forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const target = document.querySelector(link.getAttribute('href') || '');
          if (target) {
            target.scrollIntoView({ behavior: 'smooth' });
          }
        });
      });
    };
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', smoothScrollPolyfill);
    } else {
      smoothScrollPolyfill();
    }
  }
}

/**
 * Performance optimizations for different devices
 */
export function applyPerformanceOptimizations(): void {
  const deviceInfo = getDeviceInfo();
  
  // Reduce animations on low-end devices
  if (deviceInfo.isMobile && deviceInfo.pixelRatio < 2) {
    const style = document.createElement('style');
    style.textContent = `
      @media (prefers-reduced-motion: no-preference) {
        * {
          animation-duration: 0.1s !important;
          transition-duration: 0.1s !important;
        }
      }
    `;
    document.head.appendChild(style);
  }
  
  // Optimize images for high DPI displays
  if (deviceInfo.pixelRatio > 1) {
    const images = document.querySelectorAll('img[data-src-2x]');
    images.forEach(img => {
      const highResSource = img.getAttribute('data-src-2x');
      if (highResSource) {
        img.setAttribute('src', highResSource);
      }
    });
  }
}

/**
 * Initialize all device compatibility features
 */
export function initializeDeviceCompatibility(): void {
  // Apply optimizations when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      optimizeTouchInteractions();
      applyMobileOptimizations();
      applyCrossBrowserFixes();
      applyPerformanceOptimizations();
    });
  } else {
    optimizeTouchInteractions();
    applyMobileOptimizations();
    applyCrossBrowserFixes();
    applyPerformanceOptimizations();
  }
}

/**
 * Utility functions for responsive design
 */
export const responsive = {
  // Check if current screen size matches breakpoint
  isBreakpoint: (breakpoint: keyof typeof BREAKPOINTS): boolean => {
    return window.innerWidth >= BREAKPOINTS[breakpoint];
  },
  
  // Get current breakpoint
  getCurrentBreakpoint: (): keyof typeof BREAKPOINTS => {
    const width = window.innerWidth;
    if (width >= BREAKPOINTS['2xl']) return '2xl';
    if (width >= BREAKPOINTS.xl) return 'xl';
    if (width >= BREAKPOINTS.lg) return 'lg';
    if (width >= BREAKPOINTS.md) return 'md';
    if (width >= BREAKPOINTS.sm) return 'sm';
    return 'xs';
  },
  
  // Check if device supports hover
  canHover: (): boolean => {
    return window.matchMedia('(hover: hover)').matches;
  },
  
  // Check if user prefers reduced motion
  prefersReducedMotion: (): boolean => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },
  
  // Check if device is in dark mode
  prefersDarkMode: (): boolean => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }
};

// Export React import for the hook
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.React = window.React || require('react');
}

export default {
  getDeviceInfo,
  useDeviceInfo,
  optimizeTouchInteractions,
  applyMobileOptimizations,
  applyCrossBrowserFixes,
  applyPerformanceOptimizations,
  initializeDeviceCompatibility,
  responsive,
  BREAKPOINTS,
  TOUCH_TARGETS
};