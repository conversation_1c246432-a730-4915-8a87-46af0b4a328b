import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Menu, X, Phone, ArrowRight, Star } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '../ui/Button';
import { Logo } from '../ui/Logo';
import { navigationItems } from '../../config/navigation';
import { useAuth } from '../../lib/auth/AuthProvider';

export function Header({ toggleSidebar }: { toggleSidebar: () => void }) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, signOut } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAccountMenuOpen, setIsAccountMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 0);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsMobileMenuOpen(false);
    setIsAccountMenuOpen(false);
  }, [location.pathname]);

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 p-4">
      {/* Glassy Navigation Container */}
      <motion.nav 
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className={`
          max-w-7xl mx-auto transition-all duration-500 ease-out
          ${isScrolled 
            ? 'backdrop-blur-2xl bg-white/25 shadow-2xl shadow-black/15 border border-white/40' 
            : 'backdrop-blur-3xl bg-white/20 shadow-xl shadow-black/10 border border-white/50'
          }
          rounded-3xl px-6 py-3
        `}
        style={{
          background: isScrolled 
            ? 'linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.25) 100%)'
            : 'linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.2) 100%)',
          backdropFilter: 'blur(40px) saturate(180%)',
          WebkitBackdropFilter: 'blur(40px) saturate(180%)',
          boxShadow: `
            inset 0 1px 0 rgba(255,255,255,0.6),
            inset 0 -1px 0 rgba(255,255,255,0.1),
            0 20px 40px rgba(0,0,0,0.1),
            0 8px 25px rgba(0,0,0,0.08)
          `
        }}
      >
                    <div className="flex items-center justify-between">
            <button onClick={toggleSidebar} className="lg:hidden text-white mr-4">
              <Menu size={24} />
            </button>
            {/* Logo */}
            <Link to="/" className="relative group flex items-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              className="transition-transform duration-200"
              >
                <Logo 
                textColor="text-white"
                  size="lg"
                  showText={true}
                />
              </motion.div>
            </Link>

                        {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2">
            {/* Navigation Pills Container */}
                         <div 
               className="flex items-center bg-white/15 backdrop-blur-xl rounded-full px-2 py-1.5 border border-white/40 shadow-inner"
               style={{
                 backdropFilter: 'blur(20px) saturate(120%)',
                 WebkitBackdropFilter: 'blur(20px) saturate(120%)',
                 boxShadow: `
                   inset 0 1px 0 rgba(255,255,255,0.4),
                   inset 0 -1px 0 rgba(255,255,255,0.1),
                   0 4px 12px rgba(0,0,0,0.05)
                 `
               }}
             >
                <Link
                  to="/residential"
                className={`px-6 py-2.5 rounded-full font-medium transition-all duration-300 text-sm ${
                    location.pathname === '/residential'
                    ? 'bg-gradient-to-r from-brand-500 to-brand-600 text-white shadow-lg shadow-brand-500/30' 
                    : 'text-white hover:bg-white/60 hover:shadow-md hover:text-gray-700'
                  }`}
                >
                  Residential
                </Link>
                <Link
                  to="/commercial"
                className={`px-6 py-2.5 rounded-full font-medium transition-all duration-300 text-sm ${
                    location.pathname === '/commercial'
                    ? 'bg-gradient-to-r from-brand-500 to-brand-600 text-white shadow-lg shadow-brand-500/30' 
                    : 'text-white hover:bg-white/60 hover:shadow-md hover:text-gray-700'
                  }`}
                >
                  Commercial
                </Link>
                {navigationItems.map((item) => (
                  <Link
                    key={item.label}
                    to={item.href}
                  className={`px-6 py-2.5 rounded-full font-medium transition-all duration-300 text-sm ${
                      location.pathname === item.href
                      ? 'bg-gradient-to-r from-brand-500 to-brand-600 text-white shadow-lg shadow-brand-500/30' 
                      : 'text-white hover:bg-white/60 hover:shadow-md hover:text-gray-700'
                    }`}
                  >
                    {item.label}
                  </Link>
                ))}
              </div>

              {/* Auth Buttons */}
              {!user ? (
              <div className="flex items-center space-x-3 ml-4">
                  <Link to="/auth/login">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button 
                      variant="outline"
                       className="px-6 py-2.5 rounded-full bg-white/20 backdrop-blur-xl border-white/50 text-white hover:bg-white/30 hover:shadow-lg hover:text-gray-700 transition-all duration-300"
                       style={{
                         backdropFilter: 'blur(20px) saturate(120%)',
                         WebkitBackdropFilter: 'blur(20px) saturate(120%)',
                         boxShadow: `
                           inset 0 1px 0 rgba(255,255,255,0.4),
                           0 4px 12px rgba(0,0,0,0.05)
                         `
                       }}
                    >
                      Sign in
                    </Button>
                  </motion.div>
                  </Link>
                  <Link to="/auth/register">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button 
                      className="px-6 py-2.5 rounded-full bg-gradient-to-r from-brand-500 to-brand-600 text-white hover:from-brand-600 hover:to-brand-700 shadow-lg shadow-brand-500/30 hover:shadow-xl hover:shadow-brand-500/40 transition-all duration-300"
                    >
                      Get Started
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  </motion.div>
                  </Link>
                </div>
              ) : (
              <div className="relative ml-4">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant="outline"
                    onClick={() => setIsAccountMenuOpen(!isAccountMenuOpen)}
                     className="px-6 py-2.5 rounded-full bg-white/20 backdrop-blur-xl border-white/50 text-white hover:bg-white/30 hover:shadow-lg hover:text-gray-700 transition-all duration-300"
                     style={{
                       backdropFilter: 'blur(20px) saturate(120%)',
                       WebkitBackdropFilter: 'blur(20px) saturate(120%)',
                       boxShadow: `
                         inset 0 1px 0 rgba(255,255,255,0.4),
                         0 4px 12px rgba(0,0,0,0.05)
                       `
                     }}
                  >
                    Account
                  </Button>
                </motion.div>

                  <AnimatePresence>
                    {isAccountMenuOpen && (
                      <motion.div
                       initial={{ opacity: 0, y: 10, scale: 0.95 }}
                       animate={{ opacity: 1, y: 0, scale: 1 }}
                       exit={{ opacity: 0, y: 10, scale: 0.95 }}
                       className="absolute right-0 mt-3 w-52 backdrop-blur-3xl bg-white/15 rounded-2xl shadow-2xl shadow-black/20 border border-white/40 py-2 z-50"
                       style={{
                         backdropFilter: 'blur(40px) saturate(180%)',
                         WebkitBackdropFilter: 'blur(40px) saturate(180%)',
                         boxShadow: `
                           inset 0 1px 0 rgba(255,255,255,0.5),
                           inset 0 -1px 0 rgba(255,255,255,0.1),
                           0 20px 40px rgba(0,0,0,0.15),
                           0 8px 25px rgba(0,0,0,0.1)
                         `
                       }}
                      >
                        <Link
                          to="/accountdashboard"
                        className="block px-4 py-3 text-white hover:bg-white/50 hover:text-gray-700 rounded-xl mx-2 transition-all duration-200"
                        >
                          Dashboard
                        </Link>
                        <Link
                          to="/accountdashboard?tab=settings"
                        className="block px-4 py-3 text-white hover:bg-white/50 hover:text-gray-700 rounded-xl mx-2 transition-all duration-200"
                        >
                          Settings
                        </Link>
                      <div className="border-t border-white/20 my-2 mx-2" />
                        <button
                          onClick={handleSignOut}
                        className="block w-full text-left px-4 py-3 text-red-600 hover:bg-red-50/70 rounded-xl mx-2 transition-all duration-200"
                        >
                          Sign Out
                        </button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <motion.button
             whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
             className="lg:hidden p-3 rounded-full bg-white/20 backdrop-blur-xl border border-white/50 shadow-lg hover:bg-white/30 transition-all duration-300"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
             style={{
               backdropFilter: 'blur(20px) saturate(120%)',
               WebkitBackdropFilter: 'blur(20px) saturate(120%)',
               boxShadow: `
                 inset 0 1px 0 rgba(255,255,255,0.4),
                 0 4px 12px rgba(0,0,0,0.05)
               `
             }}
            >
              <AnimatePresence mode="wait">
                {isMobileMenuOpen ? (
                  <motion.div
                    key="close"
                  initial={{ rotate: -90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: 90, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  >
                  <X className="text-white w-5 h-5" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                  initial={{ rotate: 90, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  exit={{ rotate: -90, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  >
                  <Menu className="text-white w-5 h-5" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
      </motion.nav>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
             initial={{ opacity: 0, y: -20, scale: 0.95 }}
             animate={{ opacity: 1, y: 0, scale: 1 }}
             exit={{ opacity: 0, y: -20, scale: 0.95 }}
             transition={{ duration: 0.3, ease: "easeOut" }}
             className="lg:hidden mt-4 max-w-7xl mx-auto backdrop-blur-3xl bg-white/15 rounded-3xl shadow-2xl shadow-black/20 border border-white/40 overflow-hidden"
             style={{
               backdropFilter: 'blur(40px) saturate(180%)',
               WebkitBackdropFilter: 'blur(40px) saturate(180%)',
               boxShadow: `
                 inset 0 1px 0 rgba(255,255,255,0.5),
                 inset 0 -1px 0 rgba(255,255,255,0.1),
                 0 20px 40px rgba(0,0,0,0.15),
                 0 8px 25px rgba(0,0,0,0.1)
               `
             }}
            >
            <div className="divide-y divide-white/20">
                {/* Contact Info */}
              <div className="p-6">
                <div 
                  className="flex flex-col space-y-4 p-4 bg-gradient-to-r from-brand-50/30 to-brand-100/20 backdrop-blur-xl rounded-xl border border-brand-200/40"
                  style={{
                    backdropFilter: 'blur(20px) saturate(120%)',
                    WebkitBackdropFilter: 'blur(20px) saturate(120%)',
                    boxShadow: `
                      inset 0 1px 0 rgba(255,255,255,0.4),
                      0 4px 12px rgba(0,0,0,0.05)
                    `
                  }}
                >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 mr-1.5" />
                      <span className="text-sm text-white">4.9/5 Rating</span>
                      </div>
                    <span className="text-sm text-white">500+ Reviews</span>
                    </div>
                    <a 
                      href="tel:+17187171502" 
                    className="flex items-center text-white font-medium"
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      (*************
                    </a>
                  </div>
                </div>

                {/* Navigation Links */}
              <div className="py-4 px-6 space-y-2">
                  <Link
                    to="/residential"
                  className={`block px-4 py-3 rounded-xl transition-all duration-300 ${
                      location.pathname === '/residential'
                      ? 'bg-gradient-to-r from-brand-500 to-brand-600 text-white shadow-lg'
                      : 'text-white hover:bg-white/60 hover:shadow-md hover:text-gray-700'
                    }`}
                  >
                    Residential
                  </Link>
                  <Link
                    to="/commercial"
                  className={`block px-4 py-3 rounded-xl transition-all duration-300 ${
                      location.pathname === '/commercial'
                      ? 'bg-gradient-to-r from-brand-500 to-brand-600 text-white shadow-lg'
                      : 'text-white hover:bg-white/60 hover:shadow-md hover:text-gray-700'
                    }`}
                  >
                    Commercial
                  </Link>
                  {navigationItems.map((item) => (
                    <Link
                      key={item.label}
                      to={item.href}
                    className={`block px-4 py-3 rounded-xl transition-all duration-300 ${
                        location.pathname === item.href
                        ? 'bg-gradient-to-r from-brand-500 to-brand-600 text-white shadow-lg'
                        : 'text-white hover:bg-white/60 hover:shadow-md hover:text-gray-700'
                      }`}
                    >
                      {item.label}
                    </Link>
                  ))}
                </div>
                
                {/* Auth Actions */}
                {!user ? (
                <div className="p-6 space-y-4">
                    <Link to="/auth/login" className="block">
                      <Button 
                        variant="outline" 
                      className="w-full justify-center py-3 rounded-xl bg-white/20 backdrop-blur-xl border-white/50 text-white hover:bg-white/30 hover:shadow-lg hover:text-gray-700 transition-all duration-300"
                      style={{
                        backdropFilter: 'blur(20px) saturate(120%)',
                        WebkitBackdropFilter: 'blur(20px) saturate(120%)',
                        boxShadow: `
                          inset 0 1px 0 rgba(255,255,255,0.4),
                          0 4px 12px rgba(0,0,0,0.05)
                        `
                      }}
                      >
                        Sign in
                      </Button>
                    </Link>
                    <Link to="/auth/register" className="block">
                    <Button className="w-full justify-center py-3 rounded-xl bg-gradient-to-r from-brand-500 to-brand-600 text-white hover:from-brand-600 hover:to-brand-700 shadow-lg shadow-brand-500/30 hover:shadow-xl transition-all duration-300">
                        Get Started
                        <ArrowRight className="ml-2 w-4 h-4" />
                      </Button>
                    </Link>
                  </div>
                ) : (
                <div className="p-6 space-y-4">
                    <Link to="/accountdashboard" className="block">
                      <Button 
                        variant="outline" 
                      className="w-full justify-center py-3 rounded-xl bg-white/20 backdrop-blur-xl border-white/50 text-white hover:bg-white/30 hover:shadow-lg hover:text-gray-700 transition-all duration-300"
                      style={{
                        backdropFilter: 'blur(20px) saturate(120%)',
                        WebkitBackdropFilter: 'blur(20px) saturate(120%)',
                        boxShadow: `
                          inset 0 1px 0 rgba(255,255,255,0.4),
                          0 4px 12px rgba(0,0,0,0.05)
                        `
                      }}
                      >
                        Dashboard
                      </Button>
                    </Link>
                    <Button 
                      onClick={handleSignOut}
                    className="w-full justify-center py-3 rounded-xl bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg shadow-red-500/30 hover:shadow-xl transition-all duration-300"
                    >
                      Sign Out
                    </Button>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
    </header>
  );
}