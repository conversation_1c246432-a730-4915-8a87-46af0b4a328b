/**
 * Production Alerting and Notification System
 * 
 * Automated alerting system that monitors critical metrics and sends
 * notifications when thresholds are exceeded or issues are detected.
 */

import { ProductionMonitor, MetricData, LogEntry } from './productionMonitor';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface Alert {
  id: string;
  name: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  resolved: boolean;
  resolvedAt?: string;
  metadata: Record<string, any>;
  notificationsSent: string[];
}

export interface NotificationChannel {
  id: string;
  type: 'email' | 'slack' | 'webhook' | 'sms';
  name: string;
  config: Record<string, any>;
  enabled: boolean;
  severityFilter: Alert['severity'][];
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  condition: AlertCondition;
  severity: Alert['severity'];
  enabled: boolean;
  cooldownMinutes: number;
  notificationChannels: string[];
  lastTriggered?: string;
  triggerCount: number;
}

export interface AlertCondition {
  type: 'metric_threshold' | 'log_pattern' | 'health_check' | 'business_rule';
  metricName?: string;
  operator: '>' | '<' | '>=' | '<=' | '==' | '!=';
  threshold: number;
  timeWindow: number; // minutes
  logLevel?: LogEntry['level'];
  logPattern?: string;
  healthStatus?: string;
}

// ============================================================================
// Alerting System Class
// ============================================================================

export class AlertingSystem {
  private static instance: AlertingSystem;
  private monitor: ProductionMonitor;
  private alerts: Alert[] = [];
  private alertRules: AlertRule[] = [];
  private notificationChannels: NotificationChannel[] = [];
  private isInitialized = false;

  private constructor() {
    this.monitor = ProductionMonitor.getInstance();
  }

  static getInstance(): AlertingSystem {
    if (!AlertingSystem.instance) {
      AlertingSystem.instance = new AlertingSystem();
    }
    return AlertingSystem.instance;
  }

  /**
   * Initialize the alerting system
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load configuration
      await this.loadConfiguration();

      // Start monitoring
      this.startMonitoring();

      this.isInitialized = true;
      console.log('Alerting system initialized successfully');

    } catch (error) {
      console.error('Failed to initialize alerting system:', error);
      throw error;
    }
  }

  /**
   * Add or update an alert rule
   */
  addAlertRule(rule: Omit<AlertRule, 'id' | 'triggerCount'>): string {
    const alertRule: AlertRule = {
      ...rule,
      id: this.generateId(),
      triggerCount: 0
    };

    this.alertRules.push(alertRule);
    return alertRule.id;
  }

  /**
   * Add or update a notification channel
   */
  addNotificationChannel(channel: Omit<NotificationChannel, 'id'>): string {
    const notificationChannel: NotificationChannel = {
      ...channel,
      id: this.generateId()
    };

    this.notificationChannels.push(notificationChannel);
    return notificationChannel.id;
  }

  /**
   * Trigger an alert
   */
  async triggerAlert(
    name: string,
    severity: Alert['severity'],
    message: string,
    metadata: Record<string, any> = {}
  ): Promise<string> {
    const alert: Alert = {
      id: this.generateId(),
      name,
      severity,
      message,
      timestamp: new Date().toISOString(),
      resolved: false,
      metadata,
      notificationsSent: []
    };

    this.alerts.push(alert);

    // Send notifications
    await this.sendNotifications(alert);

    // Log the alert
    this.monitor.log(
      severity === 'critical' ? 'critical' : 'error',
      'system',
      `Alert triggered: ${name}`,
      {
        alertId: alert.id,
        severity,
        message,
        ...metadata
      }
    );

    return alert.id;
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string, resolvedBy?: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (!alert || alert.resolved) return false;

    alert.resolved = true;
    alert.resolvedAt = new Date().toISOString();
    alert.metadata.resolvedBy = resolvedBy;

    // Log resolution
    this.monitor.log(
      'info',
      'system',
      `Alert resolved: ${alert.name}`,
      {
        alertId,
        resolvedBy,
        duration: Date.now() - new Date(alert.timestamp).getTime()
      }
    );

    return true;
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(severity?: Alert['severity']): Alert[] {
    let alerts = this.alerts.filter(a => !a.resolved);
    
    if (severity) {
      alerts = alerts.filter(a => a.severity === severity);
    }

    return alerts.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }

  /**
   * Get alert history
   */
  getAlertHistory(limit: number = 100): Alert[] {
    return this.alerts
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Get alert statistics
   */
  getAlertStatistics(): {
    total: number;
    active: number;
    resolved: number;
    bySeverity: Record<Alert['severity'], number>;
    byRule: Record<string, number>;
  } {
    const stats = {
      total: this.alerts.length,
      active: this.alerts.filter(a => !a.resolved).length,
      resolved: this.alerts.filter(a => a.resolved).length,
      bySeverity: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0
      } as Record<Alert['severity'], number>,
      byRule: {} as Record<string, number>
    };

    this.alerts.forEach(alert => {
      stats.bySeverity[alert.severity]++;
      stats.byRule[alert.name] = (stats.byRule[alert.name] || 0) + 1;
    });

    return stats;
  }

  // Private methods
  private async loadConfiguration(): Promise<void> {
    // Load default alert rules
    this.alertRules = [
      {
        id: 'high-error-rate',
        name: 'High Error Rate',
        description: 'Error rate exceeds 5% in the last 5 minutes',
        condition: {
          type: 'metric_threshold',
          metricName: 'system.error_rate',
          operator: '>',
          threshold: 5,
          timeWindow: 5
        },
        severity: 'high',
        enabled: true,
        cooldownMinutes: 15,
        notificationChannels: ['default-email'],
        triggerCount: 0
      },
      {
        id: 'payment-failure-spike',
        name: 'Payment Failure Spike',
        description: 'Payment failure rate exceeds 10% in the last 10 minutes',
        condition: {
          type: 'metric_threshold',
          metricName: 'payment.failure_rate',
          operator: '>',
          threshold: 10,
          timeWindow: 10
        },
        severity: 'critical',
        enabled: true,
        cooldownMinutes: 5,
        notificationChannels: ['default-email', 'urgent-slack'],
        triggerCount: 0
      },
      {
        id: 'slow-response-time',
        name: 'Slow Response Time',
        description: 'Average response time exceeds 2 seconds',
        condition: {
          type: 'metric_threshold',
          metricName: 'performance.response_time',
          operator: '>',
          threshold: 2000,
          timeWindow: 5
        },
        severity: 'medium',
        enabled: true,
        cooldownMinutes: 10,
        notificationChannels: ['default-email'],
        triggerCount: 0
      },
      {
        id: 'system-health-critical',
        name: 'System Health Critical',
        description: 'System health status is critical',
        condition: {
          type: 'health_check',
          operator: '==',
          threshold: 0,
          timeWindow: 1,
          healthStatus: 'critical'
        },
        severity: 'critical',
        enabled: true,
        cooldownMinutes: 5,
        notificationChannels: ['default-email', 'urgent-slack'],
        triggerCount: 0
      }
    ];

    // Load default notification channels
    this.notificationChannels = [
      {
        id: 'default-email',
        type: 'email',
        name: 'Default Email',
        config: {
          recipients: ['<EMAIL>'],
          smtpServer: process.env.SMTP_SERVER,
          smtpPort: process.env.SMTP_PORT,
          username: process.env.SMTP_USERNAME,
          password: process.env.SMTP_PASSWORD
        },
        enabled: true,
        severityFilter: ['medium', 'high', 'critical']
      },
      {
        id: 'urgent-slack',
        type: 'slack',
        name: 'Urgent Slack Channel',
        config: {
          webhookUrl: process.env.SLACK_WEBHOOK_URL,
          channel: '#alerts',
          username: 'BookingSystem Alert'
        },
        enabled: true,
        severityFilter: ['high', 'critical']
      }
    ];
  }

  private startMonitoring(): void {
    // Check alert rules every minute
    setInterval(() => {
      this.checkAlertRules();
    }, 60000);

    // Auto-resolve old alerts
    setInterval(() => {
      this.autoResolveAlerts();
    }, 300000); // 5 minutes
  }

  private async checkAlertRules(): Promise<void> {
    for (const rule of this.alertRules) {
      if (!rule.enabled) continue;

      // Check cooldown
      if (rule.lastTriggered) {
        const cooldownEnd = new Date(rule.lastTriggered).getTime() + (rule.cooldownMinutes * 60000);
        if (Date.now() < cooldownEnd) continue;
      }

      const shouldTrigger = await this.evaluateCondition(rule.condition);
      
      if (shouldTrigger) {
        await this.triggerAlert(
          rule.name,
          rule.severity,
          rule.description,
          {
            ruleId: rule.id,
            condition: rule.condition,
            triggerCount: rule.triggerCount + 1
          }
        );

        rule.lastTriggered = new Date().toISOString();
        rule.triggerCount++;
      }
    }
  }

  private async evaluateCondition(condition: AlertCondition): Promise<boolean> {
    const timeWindowMs = condition.timeWindow * 60000;
    const since = new Date(Date.now() - timeWindowMs).toISOString();

    switch (condition.type) {
      case 'metric_threshold':
        if (!condition.metricName) return false;
        
        const metrics = this.monitor.getMetrics(condition.metricName, since);
        if (metrics.length === 0) return false;

        const avgValue = metrics.reduce((sum, m) => sum + m.value, 0) / metrics.length;
        return this.compareValues(avgValue, condition.operator, condition.threshold);

      case 'health_check':
        const health = this.monitor.getSystemHealth();
        if (condition.healthStatus === 'critical') {
          return health.status === 'critical';
        }
        return false;

      case 'log_pattern':
        const logs = this.monitor.getLogs(condition.logLevel, undefined, since);
        if (condition.logPattern) {
          const pattern = new RegExp(condition.logPattern, 'i');
          return logs.some(log => pattern.test(log.message));
        }
        return logs.length > condition.threshold;

      default:
        return false;
    }
  }

  private compareValues(value: number, operator: AlertCondition['operator'], threshold: number): boolean {
    switch (operator) {
      case '>': return value > threshold;
      case '<': return value < threshold;
      case '>=': return value >= threshold;
      case '<=': return value <= threshold;
      case '==': return value === threshold;
      case '!=': return value !== threshold;
      default: return false;
    }
  }

  private async sendNotifications(alert: Alert): Promise<void> {
    const applicableChannels = this.notificationChannels.filter(channel =>
      channel.enabled &&
      channel.severityFilter.includes(alert.severity)
    );

    for (const channel of applicableChannels) {
      try {
        await this.sendNotification(channel, alert);
        alert.notificationsSent.push(channel.id);
      } catch (error) {
        console.error(`Failed to send notification via ${channel.name}:`, error);
      }
    }
  }

  private async sendNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    switch (channel.type) {
      case 'email':
        await this.sendEmailNotification(channel, alert);
        break;
      case 'slack':
        await this.sendSlackNotification(channel, alert);
        break;
      case 'webhook':
        await this.sendWebhookNotification(channel, alert);
        break;
      default:
        console.warn(`Unsupported notification channel type: ${channel.type}`);
    }
  }

  private async sendEmailNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    // Implementation would depend on email service
    console.log(`Email notification sent for alert: ${alert.name}`);
  }

  private async sendSlackNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    if (!channel.config.webhookUrl) return;

    const payload = {
      channel: channel.config.channel,
      username: channel.config.username,
      text: `🚨 *${alert.severity.toUpperCase()} ALERT*: ${alert.name}`,
      attachments: [{
        color: this.getSeverityColor(alert.severity),
        fields: [
          { title: 'Message', value: alert.message, short: false },
          { title: 'Severity', value: alert.severity, short: true },
          { title: 'Time', value: new Date(alert.timestamp).toLocaleString(), short: true }
        ]
      }]
    };

    try {
      const response = await fetch(channel.config.webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`Slack API error: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to send Slack notification:', error);
      throw error;
    }
  }

  private async sendWebhookNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    if (!channel.config.url) return;

    try {
      const response = await fetch(channel.config.url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          alert,
          timestamp: new Date().toISOString(),
          source: 'booking-system'
        })
      });

      if (!response.ok) {
        throw new Error(`Webhook error: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to send webhook notification:', error);
      throw error;
    }
  }

  private getSeverityColor(severity: Alert['severity']): string {
    switch (severity) {
      case 'critical': return 'danger';
      case 'high': return 'warning';
      case 'medium': return '#ffaa00';
      case 'low': return 'good';
      default: return '#cccccc';
    }
  }

  private autoResolveAlerts(): void {
    const autoResolveAfterMs = 24 * 60 * 60 * 1000; // 24 hours
    const cutoff = Date.now() - autoResolveAfterMs;

    this.alerts.forEach(alert => {
      if (!alert.resolved && new Date(alert.timestamp).getTime() < cutoff) {
        this.resolveAlert(alert.id, 'auto-resolved');
      }
    });
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const alertingSystem = AlertingSystem.getInstance();
