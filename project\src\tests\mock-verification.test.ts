/**
 * Mock Verification Test
 * 
 * This test verifies that our Supabase mocks are working correctly
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TestDataFactory, MockFactory } from './utils/testHelpers';
import { supabase } from '../lib/supabase/client';

// Mock dependencies
vi.mock('../lib/supabase/client', () => ({
  supabase: MockFactory.createSupabaseMock()
}));

describe('Mock Verification Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset the Supabase mock for each test
    const mockSupabase = MockFactory.createSupabaseMock();
    vi.mocked(supabase.functions.invoke).mockImplementation(mockSupabase.functions.invoke);
    vi.mocked(supabase.from).mockImplementation(mockSupabase.from);
  });

  it('should have working Supabase from mock', async () => {
    // Act
    const query = supabase.from('test_table');
    
    // Assert
    expect(query).toBeDefined();
    expect(query.select).toBeDefined();
    expect(typeof query.select).toBe('function');
  });

  it('should have working Supabase functions mock', async () => {
    // Act
    const result = await supabase.functions.invoke('test-function', { body: {} });
    
    // Assert
    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
    expect(result.error).toBeNull();
  });

  it('should support query chaining', async () => {
    // Act
    const result = await supabase
      .from('test_table')
      .select('*')
      .eq('id', 'test-id')
      .single();
    
    // Assert
    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
    expect(result.error).toBeNull();
  });

  it('should support insert operations', async () => {
    // Act
    const result = await supabase
      .from('test_table')
      .insert({ name: 'test' })
      .select()
      .single();
    
    // Assert
    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
    expect(result.error).toBeNull();
  });

  it('should support update operations', async () => {
    // Act
    const result = await supabase
      .from('test_table')
      .update({ name: 'updated' })
      .eq('id', 'test-id')
      .select()
      .single();
    
    // Assert
    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
    expect(result.error).toBeNull();
  });
});
