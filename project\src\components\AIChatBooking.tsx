import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, Bot, User, Send, CheckCircle
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Message {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: Date;
  suggestions?: string[];
  serviceType?: string;
  quote?: {
    service: string;
    price: number;
    duration: string;
    includes: string[];
  };
}

const AIChatBookingWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentStep, setCurrentStep] = useState<'greeting' | 'service' | 'details' | 'quote' | 'booking'>('greeting');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (sender: 'user' | 'ai', text: string, extras?: Partial<Message>) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      sender,
      text,
      timestamp: new Date(),
      ...extras
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleOpen = () => {
    setIsOpen(true);
    if (messages.length === 0) {
      setTimeout(() => {
        addMessage('ai', "👋 Hi there! I'm your AI cleaning assistant. I can help you get an instant quote and book cleaning services. What type of cleaning do you need today?", {
          suggestions: ["House Cleaning", "Deep Cleaning", "Office Cleaning", "Post-Construction", "Move-in/Move-out"]
        });
      }, 500);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const simulateTyping = (callback: () => void, delay = 1000) => {
    setIsTyping(true);
    setTimeout(() => {
      setIsTyping(false);
      callback();
    }, delay);
  };

  const handleServiceSelection = (service: string) => {
    addMessage('user', service);
    setCurrentStep('details');
    
    simulateTyping(() => {
      addMessage('ai', `Great choice! I'll help you with ${service.toLowerCase()}. To give you an accurate quote, I need a few details:`, {
        suggestions: ["1-2 bedrooms", "3-4 bedrooms", "5+ bedrooms", "Small office", "Large office", "Custom size"]
      });
    });
  };

  const handleSizeSelection = (size: string) => {
    addMessage('user', size);
    setCurrentStep('quote');
    
    simulateTyping(() => {
      // Generate a mock quote based on selection
      const mockQuote = generateMockQuote(size);
      addMessage('ai', `Perfect! Based on your requirements, here's your instant quote:`, {
        quote: mockQuote,
        suggestions: ["Book Now", "Modify Details", "Ask Questions", "Get More Info"]
      });
    }, 1500);
  };

  const generateMockQuote = (size: string) => {
    const quotes = {
      "1-2 bedrooms": { service: "Regular House Cleaning (1-2 bed)", price: 89, duration: "1.5-2 hours", includes: ["All rooms cleaned", "Kitchen & bathroom", "Dusting & vacuuming"] },
      "3-4 bedrooms": { service: "Regular House Cleaning (3-4 bed)", price: 149, duration: "2.5-3 hours", includes: ["All rooms cleaned", "Kitchen & bathrooms", "Dusting & vacuuming", "Window sills"] },
      "5+ bedrooms": { service: "Large House Cleaning (5+ bed)", price: 229, duration: "3.5-4 hours", includes: ["All rooms cleaned", "Multiple bathrooms", "Kitchen deep clean", "Full house dusting"] },
      "Small office": { service: "Small Office Cleaning", price: 129, duration: "2-3 hours", includes: ["Desk areas", "Common spaces", "Restrooms", "Trash removal"] },
      "Large office": { service: "Large Office Cleaning", price: 299, duration: "4-5 hours", includes: ["All workstations", "Conference rooms", "Break rooms", "Restrooms", "Reception area"] }
    };
    return quotes[size as keyof typeof quotes] || quotes["3-4 bedrooms"];
  };

  const handleBookingAction = (action: string) => {
    addMessage('user', action);
    
    if (action === "Book Now") {
      setCurrentStep('booking');
      simulateTyping(() => {
        addMessage('ai', "Excellent! Let's get your cleaning scheduled. I'll redirect you to our booking form where you can select your preferred date and time, and provide your contact details.", {
          suggestions: ["Continue to Booking", "Need More Info", "Change Service"]
        });
      });
    } else if (action === "Continue to Booking") {
      navigate('/service-form');
    }
  };

  const handleSendMessage = () => {
    if (inputValue.trim() === '') return;

    const message = inputValue.trim();
    addMessage('user', message);
    setInputValue('');

    // Simple AI response logic
    simulateTyping(() => {
      if (currentStep === 'greeting') {
        addMessage('ai', "I can help you with that! Let me know more details about your cleaning needs, or choose from the options above.", {
          suggestions: ["House Cleaning", "Deep Cleaning", "Office Cleaning", "Get Quote"]
        });
      } else {
        addMessage('ai', "Got it! Let me process that information. You can also use the quick options above for faster service.", {
          suggestions: ["Get Quote", "Schedule Now", "More Info"]
        });
      }
    });
  };

  const handleSuggestionClick = (suggestion: string) => {
    if (currentStep === 'greeting' && ["House Cleaning", "Deep Cleaning", "Office Cleaning", "Post-Construction", "Move-in/Move-out"].includes(suggestion)) {
      handleServiceSelection(suggestion);
    } else if (currentStep === 'details' && (suggestion.includes("bedroom") || suggestion.includes("office") || suggestion.includes("Custom"))) {
      handleSizeSelection(suggestion);
    } else if (currentStep === 'quote' && ["Book Now", "Modify Details", "Ask Questions", "Get More Info"].includes(suggestion)) {
      handleBookingAction(suggestion);
    } else if (currentStep === 'booking') {
      handleBookingAction(suggestion);
    } else {
      addMessage('user', suggestion);
      simulateTyping(() => {
        addMessage('ai', "Thank you for that information! How else can I help you today?");
      });
    }
  };

  return (
    <>
      <style>{`
        .glass-modal {
          background: rgba(10, 25, 47, 0.8);
          backdrop-filter: blur(32px);
          -webkit-backdrop-filter: blur(32px);
          border: 1px solid rgba(255, 255, 255, 0.08);
        }
        .glass-widget {
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(32px);
          -webkit-backdrop-filter: blur(32px);
          border: 1px solid rgba(255, 255, 255, 0.18);
          box-shadow: 0 16px 48px rgba(0, 0, 0, 0.4), 
                      inset 0 1px 0 rgba(255, 255, 255, 0.25),
                      0 0 80px rgba(16, 185, 129, 0.1);
        }
        .ai-bubble {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(16px);
          border: 1px solid rgba(255, 255, 255, 0.08);
        }
        .user-bubble {
          background: rgba(16, 185, 129, 0.15);
          backdrop-filter: blur(16px);
          border: 1px solid rgba(16, 185, 129, 0.2);
        }
        .option-chip {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(12px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.2s ease;
        }
        .option-chip:hover {
          background: rgba(16, 185, 129, 0.1);
          border-color: rgba(16, 185, 129, 0.3);
          transform: translateY(-1px);
        }
        .quote-glass {
          background: rgba(16, 185, 129, 0.08);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(16, 185, 129, 0.2);
        }
        .input-glass {
          background: rgba(255, 255, 255, 0.04);
          backdrop-filter: blur(16px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.2s ease;
        }
        .input-glass:focus {
          border-color: rgba(16, 185, 129, 0.4);
          background: rgba(255, 255, 255, 0.06);
        }
        .send-btn {
          background: rgba(16, 185, 129, 0.8);
          backdrop-filter: blur(8px);
          transition: all 0.2s ease;
        }
        .send-btn:hover {
          background: rgba(16, 185, 129, 1);
          transform: scale(1.02);
        }
        .send-btn:disabled {
          background: rgba(107, 114, 128, 0.4);
          transform: none;
        }
        .fade-in {
          animation: fadeIn 0.3s ease-out;
        }
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .pulse-dot {
          animation: pulse 2s infinite;
        }
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
        .ai-orb-container {
          animation: orbFloat 4s ease-in-out infinite;
        }
        @keyframes orbFloat {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-2px); }
        }
        .ai-outer-ring {
          animation: outerSpin 3s linear infinite;
        }
        @keyframes outerSpin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .ai-middle-ring {
          animation: middleSpin 4s linear infinite reverse;
        }
        @keyframes middleSpin {
          0% { transform: rotate(0deg) scale(1); }
          50% { transform: rotate(180deg) scale(1.05); }
          100% { transform: rotate(360deg) scale(1); }
        }
        .ai-inner-glow {
          animation: innerGlow 2.5s ease-in-out infinite alternate;
        }
        @keyframes innerGlow {
          0% { 
            opacity: 0.6; 
            transform: scale(0.9);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
          }
          100% { 
            opacity: 1; 
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
          }
        }
        .ai-core-dot {
          animation: corePulse 1.8s ease-in-out infinite;
        }
        @keyframes corePulse {
          0%, 100% { 
            transform: scale(1);
            opacity: 0.9;
          }
          50% { 
            transform: scale(1.3);
            opacity: 1;
          }
        }
        .ai-particles {
          animation: particleFloat 6s ease-in-out infinite;
        }
        @keyframes particleFloat {
          0%, 100% { transform: rotate(0deg); opacity: 0.4; }
          25% { transform: rotate(90deg); opacity: 0.8; }
          50% { transform: rotate(180deg); opacity: 0.6; }
          75% { transform: rotate(270deg); opacity: 0.8; }
        }
      `}</style>

      <motion.div
        onClick={handleOpen}
        className="glass-widget rounded-2xl px-6 py-4 text-white shadow-lg hover:shadow-emerald-400/20 transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center justify-between w-full max-w-sm sm:max-w-md mx-auto cursor-pointer"
        whileHover={{ scale: 1.03 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex flex-col">
          <span className="font-bold text-lg whitespace-nowrap">Book Your Cleaning</span>
          <span className="text-sm text-emerald-300">Get an Instant Quote</span>
        </div>
        <div className="bg-emerald-400/20 p-3 rounded-full">
          <Bot size={24} />
        </div>
      </motion.div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center"
            onClick={handleClose}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 50 }}
              transition={{ type: 'spring', damping: 20, stiffness: 300 }}
              className="glass-modal w-full h-full sm:max-w-lg sm:h-auto sm:max-h-[90vh] sm:rounded-2xl flex flex-col overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Simple Header */}
              <div className="flex items-center justify-between p-5 border-b border-white/10">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-emerald-500 flex items-center justify-center">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium text-white">AI Assistant</h3>
                    <div className="flex items-center gap-1.5">
                      <div className="w-1.5 h-1.5 rounded-full bg-emerald-400 pulse-dot"></div>
                      <span className="text-xs text-white/60">Online</span>
                    </div>
                  </div>
                </div>
                <button
                  onClick={handleClose}
                  className="text-white/60 hover:text-white p-2 rounded-lg transition-colors"
                >
                  <X size={18} />
                </button>
              </div>

              {/* Messages Container */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <div key={message.id} className="fade-in">
                    <div className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : ''}`}>
                      {message.sender === 'ai' && (
                        <div className="w-7 h-7 rounded-full bg-emerald-500 flex items-center justify-center flex-shrink-0">
                          <Bot size={14} className="text-white" />
                        </div>
                      )}
                      
                      <div className={`max-w-xs ${message.sender === 'user' ? 'order-1' : ''}`}>
                        <div className={`p-3 rounded-2xl text-sm ${
                          message.sender === 'ai' ? 'ai-bubble text-white' : 'user-bubble text-white'
                        }`}>
                          {message.text}
                        </div>
                        
                        {/* Clean Quote Card */}
                        {message.quote && (
                          <div className="quote-glass mt-3 p-4 rounded-2xl fade-in">
                            <div className="flex items-center justify-between mb-3">
                              <span className="text-sm text-white/80">Your Quote</span>
                              <span className="text-2xl font-bold text-emerald-400">
                                ${message.quote.price}
                              </span>
                            </div>
                            <div className="text-sm text-white/90 mb-3">{message.quote.service}</div>
                            <div className="text-xs text-white/70 mb-3">Duration: {message.quote.duration}</div>
                            <div className="space-y-1">
                              {message.quote.includes.map((item, idx) => (
                                <div key={idx} className="flex items-center gap-2 text-xs text-white/70">
                                  <CheckCircle size={12} className="text-emerald-400" />
                                  <span>{item}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {/* Simple Options */}
                        {message.suggestions && (
                          <div className="flex flex-wrap gap-2 mt-3">
                            {message.suggestions.map((suggestion, idx) => (
                              <button
                                key={idx}
                                onClick={() => handleSuggestionClick(suggestion)}
                                className="option-chip px-3 py-2 rounded-xl text-xs text-white"
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                      
                      {message.sender === 'user' && (
                        <div className="w-7 h-7 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                          <User size={14} className="text-white" />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                
                {/* Simple Typing */}
                {isTyping && (
                  <div className="flex gap-3 fade-in">
                    <div className="w-7 h-7 rounded-full bg-emerald-500 flex items-center justify-center">
                      <Bot size={14} className="text-white" />
                    </div>
                    <div className="ai-bubble p-3 rounded-2xl">
                      <div className="flex gap-1 items-center">
                        <div className="w-2 h-2 bg-emerald-400 rounded-full pulse-dot"></div>
                        <div className="w-2 h-2 bg-emerald-400 rounded-full pulse-dot" style={{ animationDelay: '0.3s' }}></div>
                        <div className="w-2 h-2 bg-emerald-400 rounded-full pulse-dot" style={{ animationDelay: '0.6s' }}></div>
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>

              {/* Clean Input */}
              <div className="p-4 border-t border-white/10">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="Type your message..."
                    className="input-glass flex-1 rounded-xl py-3 px-4 text-white placeholder-white/50 focus:outline-none text-sm"
                  />
                  <button
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim()}
                    className="send-btn p-3 rounded-xl"
                  >
                    <Send size={16} className="text-white" />
                  </button>
                </div>
                <div className="text-center mt-2">
                  <span className="text-xs text-white/40">Get instant quotes • Available 24/7</span>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default AIChatBookingWidget;