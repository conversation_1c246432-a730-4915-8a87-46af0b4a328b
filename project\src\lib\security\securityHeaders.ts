/**
 * Security Headers Module
 * 
 * Implements comprehensive security headers including CSP, HSTS,
 * and other security-related HTTP headers to protect against
 * various web vulnerabilities.
 * 
 * CRITICAL: This module prevents XSS, clickjacking, MITM attacks,
 * and other client-side security vulnerabilities.
 */

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface SecurityHeadersConfig {
  contentSecurityPolicy: {
    enabled: boolean;
    directives: Record<string, string[]>;
    reportOnly: boolean;
    reportUri?: string;
  };
  strictTransportSecurity: {
    enabled: boolean;
    maxAge: number;
    includeSubDomains: boolean;
    preload: boolean;
  };
  frameOptions: {
    enabled: boolean;
    policy: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
    allowFrom?: string;
  };
  contentTypeOptions: {
    enabled: boolean;
  };
  referrerPolicy: {
    enabled: boolean;
    policy: string;
  };
  permissionsPolicy: {
    enabled: boolean;
    directives: Record<string, string[]>;
  };
}

export interface SecurityHeadersResult {
  headers: Record<string, string>;
  warnings: string[];
  errors: string[];
}

// ============================================================================
// Security Headers Class
// ============================================================================

export class SecurityHeaders {
  private static instance: SecurityHeaders;
  
  private readonly config: SecurityHeadersConfig = {
    contentSecurityPolicy: {
      enabled: true,
      reportOnly: false,
      directives: {
        'default-src': ["'self'"],
        'script-src': [
          "'self'",
          "'unsafe-inline'", // Required for React development
          "'unsafe-eval'", // Required for development tools
          'https://js.squareup.com', // Square payment processing
          'https://js.squareupsandbox.com', // Square sandbox
          'https://connect.squareup.com',
          'https://connect.squareupsandbox.com'
        ],
        'style-src': [
          "'self'",
          "'unsafe-inline'", // Required for styled-components and CSS-in-JS
          'https://fonts.googleapis.com'
        ],
        'font-src': [
          "'self'",
          'https://fonts.gstatic.com',
          'data:'
        ],
        'img-src': [
          "'self'",
          'data:',
          'blob:',
          'https:',
          'https://images.unsplash.com', // For demo images
          'https://via.placeholder.com' // For placeholder images
        ],
        'connect-src': [
          "'self'",
          'https://*.supabase.co', // Supabase API
          'https://*.supabase.in', // Supabase API
          'https://api.square.com', // Square API
          'https://connect.squareup.com', // Square Connect
          'https://connect.squareupsandbox.com', // Square Sandbox
          'wss://*.supabase.co', // Supabase WebSocket
          'wss://*.supabase.in' // Supabase WebSocket
        ],
        'frame-src': [
          "'self'",
          'https://js.squareup.com', // Square payment forms
          'https://js.squareupsandbox.com' // Square sandbox forms
        ],
        'object-src': ["'none'"],
        'base-uri': ["'self'"],
        'form-action': [
          "'self'",
          'https://squareup.com', // Square payment processing
          'https://squareupsandbox.com' // Square sandbox
        ],
        'frame-ancestors': ["'none'"],
        'upgrade-insecure-requests': []
      }
    },
    strictTransportSecurity: {
      enabled: true,
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true
    },
    frameOptions: {
      enabled: true,
      policy: 'DENY'
    },
    contentTypeOptions: {
      enabled: true
    },
    referrerPolicy: {
      enabled: true,
      policy: 'strict-origin-when-cross-origin'
    },
    permissionsPolicy: {
      enabled: true,
      directives: {
        'camera': ["'none'"],
        'microphone': ["'none'"],
        'geolocation': ["'self'"],
        'payment': ["'self'", 'https://js.squareup.com', 'https://js.squareupsandbox.com'],
        'usb': ["'none'"],
        'bluetooth': ["'none'"],
        'magnetometer': ["'none'"],
        'gyroscope': ["'none'"],
        'accelerometer': ["'none'"]
      }
    }
  };

  private constructor() {}

  static getInstance(): SecurityHeaders {
    if (!SecurityHeaders.instance) {
      SecurityHeaders.instance = new SecurityHeaders();
    }
    return SecurityHeaders.instance;
  }

  /**
   * Generate security headers for the application
   */
  generateHeaders(environment: 'development' | 'staging' | 'production' = 'production'): SecurityHeadersResult {
    const headers: Record<string, string> = {};
    const warnings: string[] = [];
    const errors: string[] = [];

    try {
      // Adjust configuration based on environment
      const envConfig = this.getEnvironmentConfig(environment);

      // Content Security Policy
      if (envConfig.contentSecurityPolicy.enabled) {
        const cspHeader = this.buildCSPHeader(envConfig.contentSecurityPolicy);
        const headerName = envConfig.contentSecurityPolicy.reportOnly 
          ? 'Content-Security-Policy-Report-Only' 
          : 'Content-Security-Policy';
        headers[headerName] = cspHeader;
      }

      // Strict Transport Security
      if (envConfig.strictTransportSecurity.enabled && environment === 'production') {
        headers['Strict-Transport-Security'] = this.buildHSTSHeader(envConfig.strictTransportSecurity);
      } else if (envConfig.strictTransportSecurity.enabled && environment !== 'production') {
        warnings.push('HSTS disabled in non-production environment');
      }

      // X-Frame-Options
      if (envConfig.frameOptions.enabled) {
        headers['X-Frame-Options'] = this.buildFrameOptionsHeader(envConfig.frameOptions);
      }

      // X-Content-Type-Options
      if (envConfig.contentTypeOptions.enabled) {
        headers['X-Content-Type-Options'] = 'nosniff';
      }

      // Referrer Policy
      if (envConfig.referrerPolicy.enabled) {
        headers['Referrer-Policy'] = envConfig.referrerPolicy.policy;
      }

      // Permissions Policy
      if (envConfig.permissionsPolicy.enabled) {
        headers['Permissions-Policy'] = this.buildPermissionsPolicyHeader(envConfig.permissionsPolicy);
      }

      // Additional security headers
      headers['X-XSS-Protection'] = '1; mode=block';
      headers['X-DNS-Prefetch-Control'] = 'off';
      headers['X-Download-Options'] = 'noopen';
      headers['X-Permitted-Cross-Domain-Policies'] = 'none';

      // Cross-Origin headers
      headers['Cross-Origin-Embedder-Policy'] = 'unsafe-none'; // Required for payment processing
      headers['Cross-Origin-Opener-Policy'] = 'same-origin-allow-popups'; // Required for payment popups
      headers['Cross-Origin-Resource-Policy'] = 'cross-origin';

    } catch (error) {
      errors.push(`Failed to generate security headers: ${error.message}`);
    }

    return { headers, warnings, errors };
  }

  /**
   * Get environment-specific configuration
   */
  private getEnvironmentConfig(environment: string): SecurityHeadersConfig {
    const config = JSON.parse(JSON.stringify(this.config)); // Deep clone

    if (environment === 'development') {
      // Relax CSP for development
      config.contentSecurityPolicy.directives['script-src'].push(
        'http://localhost:*',
        'ws://localhost:*',
        "'unsafe-eval'"
      );
      config.contentSecurityPolicy.directives['connect-src'].push(
        'http://localhost:*',
        'ws://localhost:*'
      );
      config.contentSecurityPolicy.reportOnly = true;
    }

    if (environment === 'staging') {
      // Slightly relaxed for staging
      config.contentSecurityPolicy.reportOnly = false;
    }

    return config;
  }

  /**
   * Build Content Security Policy header
   */
  private buildCSPHeader(cspConfig: SecurityHeadersConfig['contentSecurityPolicy']): string {
    const directives: string[] = [];

    for (const [directive, sources] of Object.entries(cspConfig.directives)) {
      if (sources.length === 0) {
        directives.push(directive);
      } else {
        directives.push(`${directive} ${sources.join(' ')}`);
      }
    }

    if (cspConfig.reportUri) {
      directives.push(`report-uri ${cspConfig.reportUri}`);
    }

    return directives.join('; ');
  }

  /**
   * Build HSTS header
   */
  private buildHSTSHeader(hstsConfig: SecurityHeadersConfig['strictTransportSecurity']): string {
    let header = `max-age=${hstsConfig.maxAge}`;
    
    if (hstsConfig.includeSubDomains) {
      header += '; includeSubDomains';
    }
    
    if (hstsConfig.preload) {
      header += '; preload';
    }
    
    return header;
  }

  /**
   * Build Frame Options header
   */
  private buildFrameOptionsHeader(frameConfig: SecurityHeadersConfig['frameOptions']): string {
    if (frameConfig.policy === 'ALLOW-FROM' && frameConfig.allowFrom) {
      return `ALLOW-FROM ${frameConfig.allowFrom}`;
    }
    return frameConfig.policy;
  }

  /**
   * Build Permissions Policy header
   */
  private buildPermissionsPolicyHeader(permissionsConfig: SecurityHeadersConfig['permissionsPolicy']): string {
    const directives: string[] = [];

    for (const [directive, allowlist] of Object.entries(permissionsConfig.directives)) {
      if (allowlist.length === 0) {
        directives.push(`${directive}=()`);
      } else {
        directives.push(`${directive}=(${allowlist.join(' ')})`);
      }
    }

    return directives.join(', ');
  }

  /**
   * Validate security headers configuration
   */
  validateConfiguration(): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate CSP directives
    const csp = this.config.contentSecurityPolicy;
    if (csp.enabled) {
      if (!csp.directives['default-src']) {
        errors.push('CSP default-src directive is required');
      }

      if (csp.directives['script-src']?.includes("'unsafe-eval'")) {
        warnings.push("CSP allows 'unsafe-eval' which may be dangerous in production");
      }

      if (csp.directives['script-src']?.includes("'unsafe-inline'")) {
        warnings.push("CSP allows 'unsafe-inline' scripts which reduces security");
      }
    }

    // Validate HSTS configuration
    const hsts = this.config.strictTransportSecurity;
    if (hsts.enabled && hsts.maxAge < 86400) {
      warnings.push('HSTS max-age should be at least 1 day (86400 seconds)');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Update CSP directive
   */
  updateCSPDirective(directive: string, sources: string[]): void {
    this.config.contentSecurityPolicy.directives[directive] = sources;
  }

  /**
   * Add CSP source to directive
   */
  addCSPSource(directive: string, source: string): void {
    if (!this.config.contentSecurityPolicy.directives[directive]) {
      this.config.contentSecurityPolicy.directives[directive] = [];
    }
    
    if (!this.config.contentSecurityPolicy.directives[directive].includes(source)) {
      this.config.contentSecurityPolicy.directives[directive].push(source);
    }
  }

  /**
   * Get current configuration
   */
  getConfiguration(): SecurityHeadersConfig {
    return JSON.parse(JSON.stringify(this.config));
  }

  /**
   * Generate meta tags for HTML head
   */
  generateMetaTags(environment: 'development' | 'staging' | 'production' = 'production'): string[] {
    const { headers } = this.generateHeaders(environment);
    const metaTags: string[] = [];

    // Convert headers to meta tags where applicable
    if (headers['Content-Security-Policy']) {
      metaTags.push(`<meta http-equiv="Content-Security-Policy" content="${headers['Content-Security-Policy']}">`);
    }

    if (headers['X-Frame-Options']) {
      metaTags.push(`<meta http-equiv="X-Frame-Options" content="${headers['X-Frame-Options']}">`);
    }

    if (headers['Referrer-Policy']) {
      metaTags.push(`<meta name="referrer" content="${headers['Referrer-Policy']}">`);
    }

    return metaTags;
  }
}

// Export singleton instance
export const securityHeaders = SecurityHeaders.getInstance();
