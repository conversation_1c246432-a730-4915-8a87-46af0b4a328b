/*
  # Standardize Service Types Migration
  
  This migration implements the standardized service type system to fix
  critical inconsistencies that were causing booking failures.
  
  CRITICAL FIXES:
  1. Standardizes all service type constraints across tables
  2. Migrates legacy service type values to new standard format
  3. Ensures consistency between frontend forms and database
  4. Adds proper validation and constraints
  
  Evidence: Multiple booking failures due to service type mismatches
  between form submissions and database constraints.
*/

-- ============================================================================
-- STEP 1: Drop all existing conflicting service type constraints
-- ============================================================================

-- Drop all existing service type constraints to start fresh
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS valid_service_type;
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS booking_forms_service_type_check;
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS check_service_type;

-- Drop payment table constraints if they exist
ALTER TABLE payment_records DROP CONSTRAINT IF EXISTS payment_records_service_type_check;

-- ============================================================================
-- STEP 2: Migrate legacy service type values to standardized format
-- ============================================================================

-- Migrate legacy residential service types
UPDATE booking_forms 
SET service_type = 'residential_regular' 
WHERE service_type IN ('Regular House Cleaning', 'regular', 'house', 'home');

UPDATE booking_forms 
SET service_type = 'residential_deep' 
WHERE service_type IN ('Deep House Cleaning', 'deep', 'deep-clean');

UPDATE booking_forms 
SET service_type = 'residential_move' 
WHERE service_type IN ('Move-in/Move-out Cleaning', 'move', 'move-out', 'moving');

-- Migrate commercial service types
UPDATE booking_forms 
SET service_type = 'office' 
WHERE service_type IN ('commercial', 'business', 'workplace');

UPDATE booking_forms 
SET service_type = 'sanitization' 
WHERE service_type IN ('Commercial Sanitization Service', 'disinfection', 'sanitize');

-- Migrate any remaining non-standard residential types
UPDATE booking_forms 
SET service_type = 'residential' 
WHERE service_type LIKE '%residential%' 
  AND service_type NOT IN (
    'residential_regular', 'residential_deep', 'residential_move', 'residential'
  );

-- ============================================================================
-- STEP 3: Apply standardized service type constraints
-- ============================================================================

-- Create comprehensive service type constraint based on ServiceTypeRegistry
ALTER TABLE booking_forms 
ADD CONSTRAINT booking_forms_service_type_check CHECK (
  service_type IN (
    -- Residential Services
    'residential_regular',
    'residential_deep', 
    'residential_move',
    'residential',
    
    -- Commercial Services
    'office',
    'carpet',
    'window',
    'construction',
    'sanitization',
    'tile',
    'pressure',
    'floor',
    'pool',
    'chimney',
    'waste-management'
  )
);

-- Apply same constraint to payment_records table
ALTER TABLE payment_records 
ADD CONSTRAINT payment_records_service_type_check CHECK (
  service_type IN (
    -- Residential Services
    'residential_regular',
    'residential_deep', 
    'residential_move',
    'residential',
    
    -- Commercial Services
    'office',
    'carpet',
    'window',
    'construction',
    'sanitization',
    'tile',
    'pressure',
    'floor',
    'pool',
    'chimney',
    'waste-management'
  )
);

-- ============================================================================
-- STEP 4: Update payment records to match booking forms
-- ============================================================================

-- Migrate payment records to use standardized service types
UPDATE payment_records 
SET service_type = 'residential_regular' 
WHERE service_type IN ('Regular House Cleaning', 'regular', 'house');

UPDATE payment_records 
SET service_type = 'residential_deep' 
WHERE service_type IN ('Deep House Cleaning', 'deep');

UPDATE payment_records 
SET service_type = 'residential_move' 
WHERE service_type IN ('Move-in/Move-out Cleaning', 'move', 'move-out');

UPDATE payment_records 
SET service_type = 'office' 
WHERE service_type IN ('commercial', 'business');

-- ============================================================================
-- STEP 5: Add helpful functions for service type management
-- ============================================================================

-- Function to validate service type
CREATE OR REPLACE FUNCTION is_valid_service_type(service_type text)
RETURNS boolean AS $$
BEGIN
  RETURN service_type IN (
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'office', 'carpet', 'window', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management'
  );
END;
$$ LANGUAGE plpgsql;

-- Function to normalize service type (matches frontend logic)
CREATE OR REPLACE FUNCTION normalize_service_type(input_service_type text)
RETURNS text AS $$
BEGIN
  -- Handle null/empty input
  IF input_service_type IS NULL OR input_service_type = '' THEN
    RETURN 'residential';
  END IF;
  
  -- Direct matches
  IF is_valid_service_type(input_service_type) THEN
    RETURN input_service_type;
  END IF;
  
  -- Legacy mappings
  CASE LOWER(TRIM(input_service_type))
    WHEN 'regular house cleaning', 'regular', 'house' THEN
      RETURN 'residential_regular';
    WHEN 'deep house cleaning', 'deep' THEN
      RETURN 'residential_deep';
    WHEN 'move-in/move-out cleaning', 'move', 'move-out' THEN
      RETURN 'residential_move';
    WHEN 'commercial', 'business', 'office' THEN
      RETURN 'office';
    WHEN 'commercial sanitization service', 'sanitization' THEN
      RETURN 'sanitization';
    ELSE
      -- Default fallback
      RETURN 'residential';
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- STEP 6: Add triggers to automatically normalize service types
-- ============================================================================

-- Trigger function to normalize service types on insert/update
CREATE OR REPLACE FUNCTION normalize_service_type_trigger()
RETURNS TRIGGER AS $$
BEGIN
  NEW.service_type = normalize_service_type(NEW.service_type);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to booking_forms
DROP TRIGGER IF EXISTS normalize_booking_service_type ON booking_forms;
CREATE TRIGGER normalize_booking_service_type
  BEFORE INSERT OR UPDATE ON booking_forms
  FOR EACH ROW EXECUTE FUNCTION normalize_service_type_trigger();

-- Apply trigger to payment_records
DROP TRIGGER IF EXISTS normalize_payment_service_type ON payment_records;
CREATE TRIGGER normalize_payment_service_type
  BEFORE INSERT OR UPDATE ON payment_records
  FOR EACH ROW EXECUTE FUNCTION normalize_service_type_trigger();

-- ============================================================================
-- STEP 7: Add indexes for better performance
-- ============================================================================

-- Add index on service_type for faster queries
CREATE INDEX IF NOT EXISTS idx_booking_forms_service_type 
ON booking_forms(service_type);

CREATE INDEX IF NOT EXISTS idx_payment_records_service_type 
ON payment_records(service_type);

-- ============================================================================
-- STEP 8: Add helpful views for service type analysis
-- ============================================================================

-- View to analyze service type distribution
CREATE OR REPLACE VIEW service_type_analysis AS
SELECT 
  service_type,
  COUNT(*) as booking_count,
  COUNT(DISTINCT user_id) as unique_users,
  MIN(created_at) as first_booking,
  MAX(created_at) as latest_booking
FROM booking_forms 
GROUP BY service_type
ORDER BY booking_count DESC;

-- Grant access to the view
GRANT SELECT ON service_type_analysis TO authenticated;
GRANT SELECT ON service_type_analysis TO service_role;

-- ============================================================================
-- VERIFICATION QUERIES (for testing)
-- ============================================================================

-- These queries can be used to verify the migration worked correctly:

-- 1. Check service type distribution
-- SELECT * FROM service_type_analysis;

-- 2. Verify no invalid service types remain
-- SELECT service_type, COUNT(*) FROM booking_forms 
-- WHERE NOT is_valid_service_type(service_type) 
-- GROUP BY service_type;

-- 3. Test normalization function
-- SELECT normalize_service_type('Regular House Cleaning');
-- SELECT normalize_service_type('Deep House Cleaning');

COMMENT ON TABLE booking_forms IS 'Booking forms with standardized service types';
COMMENT ON TABLE payment_records IS 'Payment records with standardized service types';
COMMENT ON FUNCTION normalize_service_type(text) IS 'Normalizes service type input to standard format';
COMMENT ON VIEW service_type_analysis IS 'Analysis of service type usage patterns';
