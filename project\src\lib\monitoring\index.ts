/**
 * Production Monitoring System - Main Entry Point
 * 
 * Centralized initialization and configuration for the complete
 * production monitoring infrastructure.
 */

import { ProductionMonitor, initializeMonitoring } from './productionMonitor';
import { BookingMonitoringIntegration, initializeBookingMonitoring } from './bookingMonitoringIntegration';
import { AlertingSystem } from './alertingSystem';

// ============================================================================
// Main Monitoring System
// ============================================================================

export class MonitoringSystem {
  private static instance: MonitoringSystem;
  private isInitialized = false;
  private monitor: ProductionMonitor;
  private alerting: AlertingSystem;

  private constructor() {
    this.monitor = ProductionMonitor.getInstance();
    this.alerting = AlertingSystem.getInstance();
  }

  static getInstance(): MonitoringSystem {
    if (!MonitoringSystem.instance) {
      MonitoringSystem.instance = new MonitoringSystem();
    }
    return MonitoringSystem.instance;
  }

  /**
   * Initialize the complete monitoring system
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🔧 Initializing Production Monitoring System...');

      // Initialize core monitoring
      await initializeMonitoring();
      console.log('✅ Core monitoring initialized');

      // Initialize booking-specific monitoring
      await initializeBookingMonitoring();
      console.log('✅ Booking monitoring initialized');

      // Initialize alerting system
      await this.alerting.initialize();
      console.log('✅ Alerting system initialized');

      // Setup global error handlers
      this.setupGlobalErrorHandlers();
      console.log('✅ Global error handlers configured');

      // Setup performance monitoring
      this.setupPerformanceMonitoring();
      console.log('✅ Performance monitoring configured');

      // Setup business metrics tracking
      this.setupBusinessMetricsTracking();
      console.log('✅ Business metrics tracking configured');

      this.isInitialized = true;
      console.log('🚀 Production Monitoring System fully initialized!');

      // Track system startup
      this.trackSystemEvent('monitoring_system_initialized', {
        timestamp: new Date().toISOString(),
        environment: import.meta.env.MODE,
        version: import.meta.env.VITE_APP_VERSION || 'unknown'
      });

    } catch (error) {
      console.error('❌ Failed to initialize monitoring system:', error);
      throw error;
    }
  }

  /**
   * Get monitoring dashboard data
   */
  getDashboardData() {
    return this.monitor.getDashboardData();
  }

  /**
   * Get system health status
   */
  getSystemHealth() {
    return this.monitor.getSystemHealth();
  }

  /**
   * Get active alerts
   */
  getActiveAlerts() {
    return this.alerting.getActiveAlerts();
  }

  /**
   * Track a custom event
   */
  trackEvent(
    category: 'booking' | 'payment' | 'user' | 'system' | 'business',
    action: string,
    metadata?: Record<string, any>
  ) {
    switch (category) {
      case 'booking':
        BookingMonitoringIntegration.trackBookingJourney(action as any, metadata || {});
        break;
      case 'payment':
        BookingMonitoringIntegration.trackPaymentProcessing(action as any, metadata || {});
        break;
      case 'user':
        BookingMonitoringIntegration.trackUserBehavior(action as any, metadata);
        break;
      case 'business':
        BookingMonitoringIntegration.trackBusinessMetrics(action as any, metadata?.value || 0, metadata);
        break;
      case 'system':
        this.trackSystemEvent(action, metadata);
        break;
    }
  }

  /**
   * Track an error
   */
  trackError(
    error: Error | string,
    context?: {
      operation?: string;
      userId?: string;
      severity?: 'low' | 'medium' | 'high' | 'critical';
      metadata?: Record<string, any>;
    }
  ) {
    BookingMonitoringIntegration.trackError(error, context || {});
  }

  /**
   * Create a custom alert
   */
  async createAlert(
    name: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    message: string,
    metadata?: Record<string, any>
  ) {
    return await this.alerting.triggerAlert(name, severity, message, metadata);
  }

  // Private methods
  private setupGlobalErrorHandlers(): void {
    // Unhandled errors
    window.addEventListener('error', (event) => {
      this.trackError(event.error || event.message, {
        operation: 'global_error_handler',
        severity: 'high',
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          type: 'javascript_error'
        }
      });
    });

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError(event.reason, {
        operation: 'unhandled_promise_rejection',
        severity: 'high',
        metadata: {
          type: 'promise_rejection',
          reason: event.reason?.toString()
        }
      });
    });

    // Resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.trackError('Resource loading failed', {
          operation: 'resource_loading',
          severity: 'medium',
          metadata: {
            type: 'resource_error',
            source: (event.target as any)?.src || (event.target as any)?.href,
            tagName: (event.target as any)?.tagName
          }
        });
      }
    }, true);
  }

  private setupPerformanceMonitoring(): void {
    // Monitor page load performance
    if (typeof window !== 'undefined' && window.performance) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          if (navigation) {
            this.monitor.recordMetric('performance.page_load_time', 
              navigation.loadEventEnd - navigation.fetchStart, 'ms');
            this.monitor.recordMetric('performance.dom_content_loaded', 
              navigation.domContentLoadedEventEnd - navigation.fetchStart, 'ms');
            this.monitor.recordMetric('performance.first_byte', 
              navigation.responseStart - navigation.fetchStart, 'ms');
          }
        }, 0);
      });

      // Monitor long tasks
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
              if (entry.duration > 50) { // Tasks longer than 50ms
                this.monitor.recordMetric('performance.long_task', entry.duration, 'ms');
                this.monitor.log('warn', 'performance', 'Long task detected', {
                  duration: entry.duration,
                  startTime: entry.startTime
                });
              }
            });
          });
          observer.observe({ entryTypes: ['longtask'] });
        } catch (error) {
          console.warn('Long task monitoring not supported:', error);
        }
      }
    }
  }

  private setupBusinessMetricsTracking(): void {
    // Track page visibility changes
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.trackEvent('user', 'session_pause');
        } else {
          this.trackEvent('user', 'session_resume');
        }
      });
    }

    // Track user engagement
    let lastActivity = Date.now();
    const trackActivity = () => {
      const now = Date.now();
      const timeSinceLastActivity = now - lastActivity;
      
      if (timeSinceLastActivity > 30000) { // 30 seconds of inactivity
        this.trackEvent('user', 'session_resume', {
          inactivityDuration: timeSinceLastActivity
        });
      }
      
      lastActivity = now;
    };

    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
      document.addEventListener(event, trackActivity, { passive: true });
    });

    // Track conversion funnel
    this.setupConversionTracking();
  }

  private setupConversionTracking(): void {
    // Track form interactions
    if (typeof document !== 'undefined') {
      document.addEventListener('focusin', (event) => {
        const target = event.target as HTMLElement;
        if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT') {
          this.trackEvent('user', 'form_field_focus', {
            fieldName: target.getAttribute('name') || target.id,
            fieldType: target.getAttribute('type') || target.tagName.toLowerCase()
          });
        }
      });

      document.addEventListener('submit', (event) => {
        const form = event.target as HTMLFormElement;
        this.trackEvent('user', 'form_submit', {
          formId: form.id,
          formAction: form.action,
          fieldCount: form.elements.length
        });
      });
    }
  }

  private trackSystemEvent(event: string, metadata?: Record<string, any>): void {
    this.monitor.log('info', 'system', `System event: ${event}`, metadata);
    this.monitor.recordMetric(`system.${event}`, 1, 'count', {
      event,
      ...metadata
    });
  }
}

// ============================================================================
// Convenience Functions
// ============================================================================

/**
 * Initialize the monitoring system (call this in your app's main entry point)
 */
export async function initializeProductionMonitoring(): Promise<MonitoringSystem> {
  const monitoring = MonitoringSystem.getInstance();
  await monitoring.initialize();
  return monitoring;
}

/**
 * Get the monitoring system instance
 */
export function getMonitoring(): MonitoringSystem {
  return MonitoringSystem.getInstance();
}

/**
 * Quick tracking functions for common events
 */
export const track = {
  booking: (stage: string, data?: any) => 
    getMonitoring().trackEvent('booking', stage, data),
  
  payment: (stage: string, data?: any) => 
    getMonitoring().trackEvent('payment', stage, data),
  
  user: (action: string, data?: any) => 
    getMonitoring().trackEvent('user', action, data),
  
  business: (metric: string, value: number, data?: any) => 
    getMonitoring().trackEvent('business', metric, { value, ...data }),
  
  error: (error: Error | string, context?: any) => 
    getMonitoring().trackError(error, context)
};

// ============================================================================
// Exports
// ============================================================================

export {
  ProductionMonitor,
  BookingMonitoringIntegration,
  AlertingSystem,
  MonitoringSystem
};

export type {
  MetricData,
  LogEntry,
  SystemHealth
} from './productionMonitor';

export type {
  Alert,
  AlertRule,
  NotificationChannel
} from './alertingSystem';
