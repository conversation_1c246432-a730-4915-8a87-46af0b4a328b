@import './styles/animations.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Root and body overflow controls */
html, body {
  overflow-x: hidden;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* Mobile scroll optimization */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

body {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  overscroll-behavior-y: contain;
}

/* Prevent horizontal scroll on mobile */
#root {
  overflow-x: hidden;
  width: 100%;
  min-height: 100vh;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-scale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delay-2 {
  animation: float 6s ease-in-out 2s infinite;
}

.animate-pulse-scale {
  animation: pulse-scale 3s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
  opacity: 0;
}

/* Aspect ratio utilities */
.aspect-w-16 {
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 16;
}

.aspect-h-9 {
  --tw-aspect-h: 9;
}

.aspect-w-16 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* Wave animations */
@keyframes wave {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-wave {
  animation: wave 15s linear infinite;
}

.animate-wave-slow {
  animation: wave 20s linear infinite;
}

.animate-wave-slower {
  animation: wave 25s linear infinite;
}

/* Form smooth scrolling and transitions */
.form-container {
  scroll-margin-top: 2rem;
}

/* Prevent layout shift during step transitions */
.step-container {
  min-height: 600px;
  transition: all 0.3s ease-in-out;
}

/* Improved grid stability for add-on services */
.addon-grid {
  display: grid;
  grid-template-rows: auto;
  align-items: stretch;
}

/* Smooth transitions for motion components */
.motion-safe {
  transform: translateZ(0);
  will-change: transform;
}

/* Mobile-specific scroll improvements */
@media (max-width: 768px) {
  body {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
  
  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}