import React from 'react';
import { motion } from 'framer-motion';
import { useDeviceInfo } from '../../utils/deviceCompatibility';

interface MobileLoaderProps {
  message?: string;
  progress?: number;
  showProgress?: boolean;
  size?: 'small' | 'medium' | 'large';
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton';
}

const MobileLoader: React.FC<MobileLoaderProps> = ({
  message = 'Loading...',
  progress,
  showProgress = false,
  size = 'medium',
  variant = 'spinner'
}) => {
  const { isMobile, deviceType } = useDeviceInfo();

  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-8 h-8',
    large: 'w-12 h-12'
  };

  const containerSizes = {
    small: 'p-4',
    medium: 'p-6',
    large: 'p-8'
  };

  const SpinnerLoader = () => (
    <motion.div
      className={`border-2 border-brand-200 border-t-brand-600 rounded-full ${sizeClasses[size]}`}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: 'linear'
      }}
    />
  );

  const DotsLoader = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className="w-2 h-2 bg-brand-600 rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            delay: index * 0.2
          }}
        />
      ))}
    </div>
  );

  const PulseLoader = () => (
    <motion.div
      className={`bg-brand-600 rounded-full ${sizeClasses[size]}`}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.7, 1, 0.7]
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut'
      }}
    />
  );

  const SkeletonLoader = () => (
    <div className="space-y-3 w-full max-w-sm">
      {[1, 2, 3].map((index) => (
        <motion.div
          key={index}
          className="h-4 bg-gray-200 rounded-md"
          style={{ width: `${100 - index * 10}%` }}
          animate={{
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: index * 0.2
          }}
        />
      ))}
    </div>
  );

  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return <DotsLoader />;
      case 'pulse':
        return <PulseLoader />;
      case 'skeleton':
        return <SkeletonLoader />;
      default:
        return <SpinnerLoader />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`flex flex-col items-center justify-center ${containerSizes[size]} ${isMobile ? 'min-h-screen' : 'min-h-[200px]'}`}
    >
      {/* Loader Animation */}
      <div className="flex items-center justify-center mb-4">
        {renderLoader()}
      </div>

      {/* Loading Message */}
      {message && (
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-gray-600 text-center font-medium"
        >
          {message}
        </motion.p>
      )}

      {/* Progress Bar */}
      {showProgress && progress !== undefined && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="w-full max-w-xs mt-4"
        >
          <div className="flex justify-between text-sm text-gray-500 mb-2">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-brand-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
            />
          </div>
        </motion.div>
      )}

      {/* Device-specific optimizations */}
      {isMobile && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="mt-6 text-xs text-gray-400 text-center px-4"
        >
          {deviceType === 'ios' && 'Optimizing for iOS...'}
          {deviceType === 'android' && 'Optimizing for Android...'}
          {deviceType === 'mobile' && 'Optimizing for mobile...'}
        </motion.div>
      )}
    </motion.div>
  );
};

// Full-screen overlay loader
export const MobileOverlayLoader: React.FC<MobileLoaderProps> = (props) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-white/95 backdrop-blur-sm z-50 flex items-center justify-center"
    >
      <MobileLoader {...props} />
    </motion.div>
  );
};

// Inline loader for components
export const MobileInlineLoader: React.FC<Omit<MobileLoaderProps, 'size'>> = (props) => {
  return (
    <div className="flex items-center justify-center py-4">
      <MobileLoader {...props} size="small" />
    </div>
  );
};

// Button loader
export const MobileButtonLoader: React.FC = () => {
  return (
    <motion.div
      className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: 'linear'
      }}
    />
  );
};

export default MobileLoader;