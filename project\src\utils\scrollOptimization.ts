/**
 * Scroll optimization utilities for better performance and user experience
 */

// Smooth scroll to element
export function smoothScrollToElement(
  element: HTMLElement | null,
  options: ScrollIntoViewOptions = {}
): void {
  if (!element) return;

  const defaultOptions: ScrollIntoViewOptions = {
    behavior: 'smooth',
    block: 'start',
    inline: 'nearest',
    ...options
  };

  // Check if browser supports smooth scrolling
  if ('scrollBehavior' in document.documentElement.style) {
    element.scrollIntoView(defaultOptions);
  } else {
    // Fallback for older browsers
    element.scrollIntoView(false);
  }
}

// Smooth scroll to top
export function smoothScrollToTop(): void {
  if ('scrollBehavior' in document.documentElement.style) {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  } else {
    // Fallback animation for older browsers
    const scrollToTop = () => {
      const c = document.documentElement.scrollTop || document.body.scrollTop;
      if (c > 0) {
        window.requestAnimationFrame(scrollToTop);
        window.scrollTo(0, c - c / 8);
      }
    };
    scrollToTop();
  }
}

// Prevent scroll during modal/overlay
export function preventScroll(): void {
  document.body.style.overflow = 'hidden';
  document.body.style.paddingRight = getScrollbarWidth() + 'px';
}

// Restore scroll after modal/overlay
export function restoreScroll(): void {
  document.body.style.overflow = '';
  document.body.style.paddingRight = '';
}

// Get scrollbar width
export function getScrollbarWidth(): number {
  const outer = document.createElement('div');
  outer.style.visibility = 'hidden';
  outer.style.overflow = 'scroll';
  document.body.appendChild(outer);

  const inner = document.createElement('div');
  outer.appendChild(inner);

  const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;

  if (outer.parentNode) {
    outer.parentNode.removeChild(outer);
  }

  return scrollbarWidth;
}

// Check if element is in viewport
export function isElementInViewport(element: HTMLElement): boolean {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

// Get scroll percentage
export function getScrollPercentage(): number {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
  return (scrollTop / scrollHeight) * 100;
}

// Mobile-specific scroll optimization
export function optimizeForMobile(): void {
  // Optimize scroll momentum on mobile
  document.body.style.setProperty('-webkit-overflow-scrolling', 'touch');
  document.body.style.setProperty('overscroll-behavior', 'contain');
}

// Initialize scroll optimizations
export function initializeScrollOptimizations(): void {
  // Set up smooth scrolling for the entire document
  document.documentElement.style.scrollBehavior = 'smooth';

  // Optimize for mobile devices
  if ('ontouchstart' in window) {
    optimizeForMobile();
  }

  // Add viewport meta tag if missing (for mobile optimization)
  const viewportMeta = document.querySelector('meta[name="viewport"]');
  if (!viewportMeta) {
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1, shrink-to-fit=no';
    document.head.appendChild(meta);
  }
}

// Scroll restoration for SPA
export function setupScrollRestoration(): void {
  if ('scrollRestoration' in history) {
    history.scrollRestoration = 'manual';
  }
}

export default {
  smoothScrollToElement,
  smoothScrollToTop,
  preventScroll,
  restoreScroll,
  getScrollbarWidth,
  isElementInViewport,
  getScrollPercentage,
  optimizeForMobile,
  initializeScrollOptimizations,
  setupScrollRestoration
}; 